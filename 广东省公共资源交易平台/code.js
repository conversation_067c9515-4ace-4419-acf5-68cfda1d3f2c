
const crypto = require('crypto');
 
const lF = "zxcvbnmlkjhgfdsaqwertyuiop0987654321QWERTYUIOPLKJHGFDSAZXCVBNM";
const fne = lF + "-@#$%^&*+!";
 
function dne(e, t) {
    switch (arguments.length) {
        case 1:
            return parseInt(Math.random() * e + 1, 10);
        case 2:
            return parseInt(Math.random() * (t - e + 1) + e, 10);
        default:
            return 0;
    }
}
 
function hne(e) {
    return [...Array(e)].map(() => lF[dne(0, 61)]).join("");
}
 
// 生成 SHA-256 哈希值
function sha256(message) {
    // 确保 message 是字符串
    if (typeof message !== "string") {
        message = JSON.stringify(message);
    }
    return crypto.createHash('sha256').update(message).digest('hex');
}
 
// 参数序列化为查询字符串
function buildQueryString(params) {
    return Object.entries(params)
        .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
        .join('&');
}
function pne(e) {
    let t = "";
    return typeof e == "object" ? t = Object.keys(e).map(n => `${n}=${e[n]}`).sort().join("&") : typeof e == "string" && (t = e.split("&").sort().join("&")),
    t
}
function t1(e={}) {
    const {p: t, t: n, n: u, k: o} = e
      , r = pne(t);
    return sha256(u + o + decodeURIComponent(r) + n)
}
 
// 主函数：动态生成请求头
function main123(pageNo) {
    // 随机生成 nonce 值
    const nonce = hne(16); // X-Dgi-Req-Nonce
    console.log('Nonce:', nonce);
 
    // 当前时间戳
    const timestamp = Date.now(); // X-Dgi-Req-Timestamp
    console.log('Timestamp:', timestamp);
 
    // 请求参数
    const params = {
        "type": "trading-type",
        "openConvert": false,
        "keyword": "",
        "siteCode": "44",
        "secondType": "A",
        "tradingProcess": "",
        "thirdType": "[]",
        "projectType": "",
        "publishStartTime": "",
        "publishEndTime": "",
        "pageNo": pageNo,
        "pageSize": 10
    };
 
    // 转换为查询字符串
    const data = buildQueryString(params);
    console.log('data:', data);
 
    // 固定秘钥
    const secretKey = "k8tUyS$m";
 
    t1({
                p: data,
                t: timestamp,
                n: nonce,
                k: "k8tUyS$m"
            })
    // 生成签名
    signature = t1({
        p: data,
        t: timestamp,
        n: nonce,
        k: secretKey
    });
    // 返回动态请求头
    const headers = {
        'X-Dgi-Req-App': 'ggzy-portal',
        'X-Dgi-Req-Nonce': nonce,
        'X-Dgi-Req-Timestamp': timestamp,
        'X-Dgi-Req-Signature': signature
    };
 
 
    return headers;
}
 
// 测试 main123 函数
const headers = main123(1); // 传入页码参数
console.log(headers);
// console.log('type=trading-type&openConvert=false&keyword=&siteCode=44&secondType=A&tradingProcess=&thirdType=%5B%5D&projectType=&publishStartTime=&publishEndTime=&pageNo=5&pageSize=10'
// )