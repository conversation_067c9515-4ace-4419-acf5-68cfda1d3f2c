/**
 * url https://www.zq12369.com/environment.php?date=2025-08-10&tab=rank&order=DESC&type=DAY#rank
 * 加密
 */
const CryptoJS = require('crypto-js');


    function ObjectSort(obj) {
        var newObject = {};
        Object.keys(obj).sort().map(function(key) {
            newObject[key] = obj[key]
        });
        return newObject
    }
        var DES = {
        encrypt: function(text,akb36,akb48) {
          
            var secretkey = (CryptoJS.MD5(akb36).toString()).substr(0, 16);
            var secretiv = (CryptoJS.MD5(akb48).toString()).substr(24, 8);
            secretkey = CryptoJS.enc.Utf8.parse(secretkey);
            secretiv = CryptoJS.enc.Utf8.parse(secretiv);
            var result = CryptoJS.DES.encrypt(text, secretkey, {
                iv: secretiv,
                mode: CryptoJS.mode.CBC,
                padding: CryptoJS.pad.Pkcs7
            });
            return result.toString()
        },
    };
    function MyEncode(str,akb36,akb48) {
        var arr = "32223".split('')
        var b = new Base64;
        arr.forEach(times => {
            switch (times) {
            case "1":
                str = AES.encrypt(str,akb36,akb48)
                break;
            case "2":
                str = DES.encrypt(str,akb36,akb48)
                break;
            case "3":
                str = b.encode(str)
                break;
            }
        }
        )
        return str;
    }
function Base64() {
	_keyStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=", this.encode = function(a) {
		var c, d, e, f, g, h, i, b = "",
			j = 0;
		for (a = _utf8_encode(a); j < a.length;) c = a.charCodeAt(j++), d = a.charCodeAt(j++), e = a.charCodeAt(j++), f = c >> 2, g = (3 & c) << 4 | d >> 4, h = (15 & d) << 2 | e >> 6, i = 63 & e, isNaN(d) ? h = i = 64 : isNaN(e) && (i = 64), b = b + _keyStr.charAt(f) + _keyStr.charAt(g) + _keyStr.charAt(h) + _keyStr.charAt(i);
		return b
	}, this.decode = function(a) {
		var c, d, e, f, g, h, i, b = "",
			j = 0;
		for (a = a.replace(/[^A-Za-z0-9\+\/\=]/g, ""); j < a.length;) f = _keyStr.indexOf(a.charAt(j++)), g = _keyStr.indexOf(a.charAt(j++)), h = _keyStr.indexOf(a.charAt(j++)), i = _keyStr.indexOf(a.charAt(j++)), c = f << 2 | g >> 4, d = (15 & g) << 4 | h >> 2, e = (3 & h) << 6 | i, b += String.fromCharCode(c), 64 != h && (b += String.fromCharCode(d)), 64 != i && (b += String.fromCharCode(e));
		return b = _utf8_decode(b)
	}, _utf8_encode = function(a) {
		var b, c, d;
		for (a = a.replace(/\r\n/g, "\n"), b = "", c = 0; c < a.length; c++) d = a.charCodeAt(c), 128 > d ? b += String.fromCharCode(d) : d > 127 && 2048 > d ? (b += String.fromCharCode(192 | d >> 6), b += String.fromCharCode(128 | 63 & d)) : (b += String.fromCharCode(224 | d >> 12), b += String.fromCharCode(128 | 63 & d >> 6), b += String.fromCharCode(128 | 63 & d));
		return b
	}, _utf8_decode = function(a) {
		for (var b = "", c = 0, d = c1 = c2 = 0; c < a.length;) d = a.charCodeAt(c), 128 > d ? (b += String.fromCharCode(d), c++) : d > 191 && 224 > d ? (c2 = a.charCodeAt(c + 1), b += String.fromCharCode((31 & d) << 6 | 63 & c2), c += 2) : (c2 = a.charCodeAt(c + 1), c3 = a.charCodeAt(c + 2), b += String.fromCharCode((15 & d) << 12 | (63 & c2) << 6 | 63 & c3), c += 3);
		return b
	}
}

function jiami(akb36,akb48) {
    const method="GETCITYAQIRANK"
const obj={
    "order": "desc"
}
        var appId = "4f0e3a273d547ce6b7147bfa7ceb4b6e";
        // var timestamp = new Date().getTime();
        var timestamp = 1754904912823;
        var need = {
            appId: appId,
            method: method,
            timestamp: timestamp,
            clienttype: 'WEB',
            object: obj,
            secret: CryptoJS.MD5(appId + method + timestamp + 'WEB' + JSON.stringify(ObjectSort(obj))).toString()
        };
        // console.log(need)
        return MyEncode(JSON.stringify(need),akb36,akb48)
    }
      akb36="mAkJqt8coXQ96zML"
            akb48="t4ABRmeN"
 res=   jiami(akb36,akb48)
 console.log(res)
