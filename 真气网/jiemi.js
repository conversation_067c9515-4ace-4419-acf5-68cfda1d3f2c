const CryptoJS = require('crypto-js');

  var DES = {
       decrypt: function(text, akb36, akb48) {
            try {
                var secretkey = (CryptoJS.MD5(akb36).toString()).substr(0, 16);
                var secretiv = (CryptoJS.MD5(akb48).toString()).substr(24, 8);
                secretkey = CryptoJS.enc.Utf8.parse(secretkey);
                secretiv = CryptoJS.enc.Utf8.parse(secretiv);
                
                console.log('DES密钥:', secretkey.toString());
                console.log('DES向量:', secretiv.toString());
                console.log('待解密数据:', text.substring(0, 50) + '...');
                
                var result = CryptoJS.DES.decrypt(text, secretkey, {
                    iv: secretiv,
                    mode: CryptoJS.mode.CBC,
                    padding: CryptoJS.pad.Pkcs7
                });
                    
                // 检查解密结果是否有效
                if (result.sigBytes <= 0) {
                    console.log('DES解密失败：结果为空');
                    return text; // 返回原文本
                }
                
                return result.toString(CryptoJS.enc.Utf8);
            } catch (error) {
                console.log('DES解密出错:', error.message);
                return text; // 解密失败时返回原文本
            }
        }
    }
    function Base64() {
	_keyStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=", this.encode = function(a) {
		var c, d, e, f, g, h, i, b = "",
			j = 0;
		for (a = _utf8_encode(a); j < a.length;) c = a.charCodeAt(j++), d = a.charCodeAt(j++), e = a.charCodeAt(j++), f = c >> 2, g = (3 & c) << 4 | d >> 4, h = (15 & d) << 2 | e >> 6, i = 63 & e, isNaN(d) ? h = i = 64 : isNaN(e) && (i = 64), b = b + _keyStr.charAt(f) + _keyStr.charAt(g) + _keyStr.charAt(h) + _keyStr.charAt(i);
		return b
	}, this.decode = function(a) {
		var c, d, e, f, g, h, i, b = "",
			j = 0;
		for (a = a.replace(/[^A-Za-z0-9\+\/\=]/g, ""); j < a.length;) f = _keyStr.indexOf(a.charAt(j++)), g = _keyStr.indexOf(a.charAt(j++)), h = _keyStr.indexOf(a.charAt(j++)), i = _keyStr.indexOf(a.charAt(j++)), c = f << 2 | g >> 4, d = (15 & g) << 4 | h >> 2, e = (3 & h) << 6 | i, b += String.fromCharCode(c), 64 != h && (b += String.fromCharCode(d)), 64 != i && (b += String.fromCharCode(e));
		return b = _utf8_decode(b)
	}, _utf8_encode = function(a) {
		var b, c, d;
		for (a = a.replace(/\r\n/g, "\n"), b = "", c = 0; c < a.length; c++) d = a.charCodeAt(c), 128 > d ? b += String.fromCharCode(d) : d > 127 && 2048 > d ? (b += String.fromCharCode(192 | d >> 6), b += String.fromCharCode(128 | 63 & d)) : (b += String.fromCharCode(224 | d >> 12), b += String.fromCharCode(128 | 63 & d >> 6), b += String.fromCharCode(128 | 63 & d));
		return b
	}, _utf8_decode = function(a) {
		for (var b = "", c = 0, d = c1 = c2 = 0; c < a.length;) d = a.charCodeAt(c), 128 > d ? (b += String.fromCharCode(d), c++) : d > 191 && 224 > d ? (c2 = a.charCodeAt(c + 1), b += String.fromCharCode((31 & d) << 6 | 63 & c2), c += 2) : (c2 = a.charCodeAt(c + 1), c3 = a.charCodeAt(c + 2), b += String.fromCharCode((15 & d) << 12 | (63 & c2) << 6 | 63 & c3), c += 3);
		return b
	}
}
    function MyDecode(str,akb36,akb48) {
        // 根据调试结果，这个数据只需要Base64解码
        var b = new Base64;
        console.log('开始解码...');
        console.log('原始数据:', str.substring(0, 100) + '...');
        
        try {
            // 直接进行Base64解码
            var decoded = b.decode(str);
            console.log('Base64解码成功');
            console.log('解码结果:', decoded.substring(0, 100) + '...');
            return decoded;
        } catch (error) {
            console.log('Base64解码失败:', error.message);
            
            // 如果直接解码失败，尝试原始的解密流程
            console.log('尝试完整解密流程...');
            var arr = '32223'.split('').reverse()
            
            arr.forEach((times, index) => {
                console.log(`步骤 ${index + 1}: 执行操作 ${times}`);
                try {
                    switch (times) {
                      case "1":
                          console.log('跳过AES解密步骤');
                          break;
                      case "2":
                          // 只有当数据看起来像加密数据时才进行DES解密
                          if (str.length > 100 && !str.startsWith('{')) {
                              str = DES.decrypt(str,akb36,akb48)
                              console.log('DES解密后:', str.substring(0, 100) + '...');
                          } else {
                              console.log('跳过DES解密（数据已是明文）');
                          }
                          break;
                      case "3":
                          str = b.decode(str)
                          console.log('Base64解码后:', str.substring(0, 100) + '...');
                          break;
                      }
                } catch (stepError) {
                    console.log(`步骤 ${index + 1} 出错:`, stepError.message);
                }
            });
            
            return str;
        }
    }
    str="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"
         let akb36="mAkJqt8coXQ96zML"
    let akb48="t4ABRmeN"
    console.log(JSON.stringify(JSON.parse(MyDecode(str,akb36,akb48)), null, 2))