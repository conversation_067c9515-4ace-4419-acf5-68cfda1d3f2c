window=global
const JSEncrypt = require('jsencrypt');
var encrypt = new JSEncrypt();
pbk=`MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQComqoAyvbCqO1EGsADwfNTWFQIUbm8CLdeb9TgjGLcz95mAo204SqTYdSEUxFsOnPfROOTxhkhfjbRxBV4/xjS06Y+kkUdiMGFtABIxRQHQIh0LrVvEZQs4NrixxcPI+b1bpE0gO/GAFSNWm9ejhZGj7UnqiHphnSJAVQNz2lgowIDAQAB`
encrypt.setPublicKey(pbk);
var encrypted = encrypt.encrypt('123456');
console.log(encrypted.length);
