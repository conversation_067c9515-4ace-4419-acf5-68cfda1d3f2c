/*
 * @LastEditors: Mishi <EMAIL>
 * @Date: 2025-08-15 00:41:32
 * @LastEditTime: 2025-08-15 00:47:42
 * @FilePath: /逆向百例/重庆工程职业技术学院/code.js
 * url https://ai.cqvie.edu.cn/center-auth-server/officeHallApplicationCode/cas/login?service=https%3A%2F%2Fai.cqvie.edu.cn%2Fump%2Fcommon%2Flogin%2FauthSourceAuth%2Fauth 
 */
    window=global
    const JSEncrypt = require('jsencrypt')
    let encryptor = new JSEncrypt()
    encryptor.setPublicKey(
        '-----BEGIN PUBLIC KEY-----'+
        'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDACwPDxYycdCiNeblZa9LjvDzb'+
        'iZU1vc9gKRcG/pGjZ/DJkI4HmoUE2r/o6SfB5az3s+H5JDzmOMVQ63hD7LZQGR4k'+
    '**********************************+gtxz4WnriDjf+e/CxJ7OD03e7sy5N'+
    'Y/akVmYNtghKZzz6jwIDAQAB'+
    '-----END PUBLIC KEY-----'
    )

    function getSecretParam (p)  {
        let arr = []
        let maxIndex = 0
        for (let i = 0; i <= p.length; i++) {
            if ((i + 1) % 30 === 0) {
                arr.push(encodeURI(encryptor.encrypt(p.substring(maxIndex, i))))
                maxIndex = i
            }
        }
        maxIndex !== p.length &&
        arr.push(
            encodeURI(encryptor.encrypt(p.substring(maxIndex, p.length)))
        )
        return JSON.stringify(arr)
    }
    pwd=getSecretParam('654321')
    console.log(pwd)
