/**
 * 
 * 解密
 * @param {*} _0x472573 
 * @param {*} _0xa6cc40 
 * @returns 
 */

// const base64js=require('base64-js')
var _0xa6cc = function(_0x472573, _0xa6cc40) {
    _0x472573 = _0x472573 - 0x0;
    var _0x27b5e6 = _0x4725[_0x472573];
    return _0x27b5e6;
};

base64js=(function() {
    var _0x3b5585, _0x1bde6a, _0x2c1544;
    return function _0x45f80d(_0x2f9564, _0x35e8d5, _0x39af48) {
        function _0xaf9ccf(_0x378a25, _0x57490e) {
            if (!_0x35e8d5[_0x378a25]) {
                if (!_0x2f9564[_0x378a25]) {
                    var _0x5892e5 = typeof require == 'function' && require;
                    if (!_0x57490e && _0x5892e5) {
                        return _0x5892e5(_0x378a25, !0x0);
                    }
                    if (_0x17ad1c) {
                        return _0x17ad1c(_0x378a25, !0x0);
                    }
                    var _0x3608ae = new Error('Cannot\x20find\x20module\x20\x27' + _0x378a25 + '\x27');
                    throw _0x3608ae['code'] = 'MODULE_NOT_FOUND',
                    _0x3608ae;
                }
                var _0x5169e3 = _0x35e8d5[_0x378a25] = {
                    'exports': {}
                };
                _0x2f9564[_0x378a25][0x0]['call'](_0x5169e3['exports'], function(_0x56abc2) {
                    var _0x5d77d8 = _0x2f9564[_0x378a25][0x1][_0x56abc2];
                    return _0xaf9ccf(_0x5d77d8 ? _0x5d77d8 : _0x56abc2);
                }, _0x5169e3, _0x5169e3[_0xa6cc('0x12')], _0x45f80d, _0x2f9564, _0x35e8d5, _0x39af48);
            }
            return _0x35e8d5[_0x378a25]['exports'];
        }
        var _0x17ad1c = typeof require == _0xa6cc('0x0') && require;
        for (var _0x74158c = 0x0; _0x74158c < _0x39af48[_0xa6cc('0x1f')]; _0x74158c++) {
            _0xaf9ccf(_0x39af48[_0x74158c]);
        }
        return _0xaf9ccf;
    }({
        '/': [function(_0xdb3df4, _0x52cce7, _0x19fb90) {
            _0x19fb90['byteLength'] = _0x16ab85;
            _0x19fb90['toByteArray'] = _0x4b7710;
            _0x19fb90[_0xa6cc('0x3')] = _0x171ea5;
            var _0x5473f7 = [];
            var _0x4e3000 = [];
            var _0x4291b8 = typeof Uint8Array !== _0xa6cc('0x1d') ? Uint8Array : Array;
            var _0x187ce9 = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
            for (var _0x5c74cc = 0x0, _0x3ee261 = _0x187ce9[_0xa6cc('0x1f')]; _0x5c74cc < _0x3ee261; ++_0x5c74cc) {
                _0x5473f7[_0x5c74cc] = _0x187ce9[_0x5c74cc];
                _0x4e3000[_0x187ce9[_0xa6cc('0x1b')](_0x5c74cc)] = _0x5c74cc;
            }
            _0x4e3000['-'[_0xa6cc('0x1b')](0x0)] = 0x3e;
            _0x4e3000['_'['charCodeAt'](0x0)] = 0x3f;
            function _0x174e2f(_0x3338fa) {
                var _0x502716 = _0x3338fa[_0xa6cc('0x1f')];
                if (_0x502716 % 0x4 > 0x0) {
                    throw new Error('Invalid\x20string.\x20Length\x20must\x20be\x20a\x20multiple\x20of\x204');
                }
                return _0x3338fa[_0x502716 - 0x2] === '=' ? 0x2 : _0x3338fa[_0x502716 - 0x1] === '=' ? 0x1 : 0x0;
            }
            function _0x16ab85(_0x3d6263) {
                return _0x3d6263['length'] * 0x3 / 0x4 - _0x174e2f(_0x3d6263);
            }
            function _0x4b7710(_0x4b02de) {
                var _0x2550bf, _0x4d039a, _0x13e62c, _0x3adc7f, _0x1c734b;
                var _0x2a8704 = _0x4b02de['length'];
                _0x3adc7f = _0x174e2f(_0x4b02de);
                _0x1c734b = new _0x4291b8(_0x2a8704 * 0x3 / 0x4 - _0x3adc7f);
                _0x4d039a = _0x3adc7f > 0x0 ? _0x2a8704 - 0x4 : _0x2a8704;
                var _0x4c2a8c = 0x0;
                for (_0x2550bf = 0x0; _0x2550bf < _0x4d039a; _0x2550bf += 0x4) {
                    _0x13e62c = _0x4e3000[_0x4b02de['charCodeAt'](_0x2550bf)] << 0x12 | _0x4e3000[_0x4b02de[_0xa6cc('0x1b')](_0x2550bf + 0x1)] << 0xc | _0x4e3000[_0x4b02de['charCodeAt'](_0x2550bf + 0x2)] << 0x6 | _0x4e3000[_0x4b02de[_0xa6cc('0x1b')](_0x2550bf + 0x3)];
                    _0x1c734b[_0x4c2a8c++] = _0x13e62c >> 0x10 & 0xff;
                    _0x1c734b[_0x4c2a8c++] = _0x13e62c >> 0x8 & 0xff;
                    _0x1c734b[_0x4c2a8c++] = _0x13e62c & 0xff;
                }
                if (_0x3adc7f === 0x2) {
                    _0x13e62c = _0x4e3000[_0x4b02de['charCodeAt'](_0x2550bf)] << 0x2 | _0x4e3000[_0x4b02de['charCodeAt'](_0x2550bf + 0x1)] >> 0x4;
                    _0x1c734b[_0x4c2a8c++] = _0x13e62c & 0xff;
                } else {
                    if (_0x3adc7f === 0x1) {
                        _0x13e62c = _0x4e3000[_0x4b02de['charCodeAt'](_0x2550bf)] << 0xa | _0x4e3000[_0x4b02de[_0xa6cc('0x1b')](_0x2550bf + 0x1)] << 0x4 | _0x4e3000[_0x4b02de[_0xa6cc('0x1b')](_0x2550bf + 0x2)] >> 0x2;
                        _0x1c734b[_0x4c2a8c++] = _0x13e62c >> 0x8 & 0xff;
                        _0x1c734b[_0x4c2a8c++] = _0x13e62c & 0xff;
                    }
                }
                return _0x1c734b;
            }
            function _0x4e549b(_0x1bc071) {
                return _0x5473f7[_0x1bc071 >> 0x12 & 0x3f] + _0x5473f7[_0x1bc071 >> 0xc & 0x3f] + _0x5473f7[_0x1bc071 >> 0x6 & 0x3f] + _0x5473f7[_0x1bc071 & 0x3f];
            }
            function _0x52333e(_0xadb06b, _0xd1b2ec, _0x5ab800) {
                var _0x2ad5f6;
                var _0x47feb8 = [];
                for (var _0x2143ef = _0xd1b2ec; _0x2143ef < _0x5ab800; _0x2143ef += 0x3) {
                    _0x2ad5f6 = (_0xadb06b[_0x2143ef] << 0x10) + (_0xadb06b[_0x2143ef + 0x1] << 0x8) + _0xadb06b[_0x2143ef + 0x2];
                    _0x47feb8[_0xa6cc('0x21')](_0x4e549b(_0x2ad5f6));
                }
                return _0x47feb8['join']('');
            }
            function _0x171ea5(_0x1b3422) {
                var _0x490c0a;
                var _0x3105b7 = _0x1b3422[_0xa6cc('0x1f')];
                var _0x255bd0 = _0x3105b7 % 0x3;
                var _0x177114 = '';
                var _0x918fd = [];
                var _0x4118a2 = 0x3fff;
                for (var _0x2282f9 = 0x0, _0x345dc3 = _0x3105b7 - _0x255bd0; _0x2282f9 < _0x345dc3; _0x2282f9 += _0x4118a2) {
                    _0x918fd[_0xa6cc('0x21')](_0x52333e(_0x1b3422, _0x2282f9, _0x2282f9 + _0x4118a2 > _0x345dc3 ? _0x345dc3 : _0x2282f9 + _0x4118a2));
                }
                if (_0x255bd0 === 0x1) {
                    _0x490c0a = _0x1b3422[_0x3105b7 - 0x1];
                    _0x177114 += _0x5473f7[_0x490c0a >> 0x2];
                    _0x177114 += _0x5473f7[_0x490c0a << 0x4 & 0x3f];
                    _0x177114 += '==';
                } else {
                    if (_0x255bd0 === 0x2) {
                        _0x490c0a = (_0x1b3422[_0x3105b7 - 0x2] << 0x8) + _0x1b3422[_0x3105b7 - 0x1];
                        _0x177114 += _0x5473f7[_0x490c0a >> 0xa];
                        _0x177114 += _0x5473f7[_0x490c0a >> 0x4 & 0x3f];
                        _0x177114 += _0x5473f7[_0x490c0a << 0x2 & 0x3f];
                        _0x177114 += '=';
                    }
                }
                _0x918fd[_0xa6cc('0x21')](_0x177114);
                return _0x918fd['join']('');
            }
        }
        , {}]
    }, {}, [])('/');
})


var _0x4725 = ['function', 'mode', 'secretKey', 'fromByteArray', 'PUT_ULONG_BE', 'sm4Sbox', 'SHL', 'slice', '5BD730C485F2AF10', 'sm4_one_round', 'fromCharCode', 'padding', 'SM4_ENCRYPT', 'sm4_setkey_enc', 'SM4_DECRYPT', 'key\x20error!', 'toString', 'sm4_crypt_ecb', 'exports', 'sm4_crypt_cbc', 'sm4F', 'trim', 'error', 'ctx\x20is\x20null!', 'GET_ULONG_BE', 'match', 'encryptData_CBC', 'charCodeAt', 'isPadding', 'undefined', 'object', 'length', 'input\x20is\x20null!', 'push', '$ShowMe$', 'ROTL'];

(function(_0x53609e) {
    if (typeof exports === _0xa6cc('0x1e') && typeof module !== 'undefined') {
        module[_0xa6cc('0x12')] = _0x53609e();
    } else {
        if (typeof define === _0xa6cc('0x0') && define['amd']) {
            define([], _0x53609e);
        } else {
            var _0x39cc8e;
            if (typeof window !== 'undefined') {
                _0x39cc8e = window;
            } else {
                if (typeof global !== _0xa6cc('0x1d')) {
                    _0x39cc8e = global;
                } else {
                    if (typeof self !== 'undefined') {
                        _0x39cc8e = self;
                    } else {
                        _0x39cc8e = this;
                    }
                }
            }
            _0x39cc8e['base64js'] = _0x53609e();
        }
    }
}(function() {
    var _0x3b5585, _0x1bde6a, _0x2c1544;
    return function _0x45f80d(_0x2f9564, _0x35e8d5, _0x39af48) {
        function _0xaf9ccf(_0x378a25, _0x57490e) {
            if (!_0x35e8d5[_0x378a25]) {
                if (!_0x2f9564[_0x378a25]) {
                    var _0x5892e5 = typeof require == 'function' && require;
                    if (!_0x57490e && _0x5892e5) {
                        return _0x5892e5(_0x378a25, !0x0);
                    }
                    if (_0x17ad1c) {
                        return _0x17ad1c(_0x378a25, !0x0);
                    }
                    var _0x3608ae = new Error('Cannot\x20find\x20module\x20\x27' + _0x378a25 + '\x27');
                    throw _0x3608ae['code'] = 'MODULE_NOT_FOUND',
                    _0x3608ae;
                }
                var _0x5169e3 = _0x35e8d5[_0x378a25] = {
                    'exports': {}
                };
                _0x2f9564[_0x378a25][0x0]['call'](_0x5169e3['exports'], function(_0x56abc2) {
                    var _0x5d77d8 = _0x2f9564[_0x378a25][0x1][_0x56abc2];
                    return _0xaf9ccf(_0x5d77d8 ? _0x5d77d8 : _0x56abc2);
                }, _0x5169e3, _0x5169e3[_0xa6cc('0x12')], _0x45f80d, _0x2f9564, _0x35e8d5, _0x39af48);
            }
            return _0x35e8d5[_0x378a25]['exports'];
        }
        var _0x17ad1c = typeof require == _0xa6cc('0x0') && require;
        for (var _0x74158c = 0x0; _0x74158c < _0x39af48[_0xa6cc('0x1f')]; _0x74158c++) {
            _0xaf9ccf(_0x39af48[_0x74158c]);
        }
        return _0xaf9ccf;
    }({
        '/': [function(_0xdb3df4, _0x52cce7, _0x19fb90) {
            _0x19fb90['byteLength'] = _0x16ab85;
            _0x19fb90['toByteArray'] = _0x4b7710;
            _0x19fb90[_0xa6cc('0x3')] = _0x171ea5;
            var _0x5473f7 = [];
            var _0x4e3000 = [];
            var _0x4291b8 = typeof Uint8Array !== _0xa6cc('0x1d') ? Uint8Array : Array;
            var _0x187ce9 = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
            for (var _0x5c74cc = 0x0, _0x3ee261 = _0x187ce9[_0xa6cc('0x1f')]; _0x5c74cc < _0x3ee261; ++_0x5c74cc) {
                _0x5473f7[_0x5c74cc] = _0x187ce9[_0x5c74cc];
                _0x4e3000[_0x187ce9[_0xa6cc('0x1b')](_0x5c74cc)] = _0x5c74cc;
            }
            _0x4e3000['-'[_0xa6cc('0x1b')](0x0)] = 0x3e;
            _0x4e3000['_'['charCodeAt'](0x0)] = 0x3f;
            function _0x174e2f(_0x3338fa) {
                var _0x502716 = _0x3338fa[_0xa6cc('0x1f')];
                if (_0x502716 % 0x4 > 0x0) {
                    throw new Error('Invalid\x20string.\x20Length\x20must\x20be\x20a\x20multiple\x20of\x204');
                }
                return _0x3338fa[_0x502716 - 0x2] === '=' ? 0x2 : _0x3338fa[_0x502716 - 0x1] === '=' ? 0x1 : 0x0;
            }
            function _0x16ab85(_0x3d6263) {
                return _0x3d6263['length'] * 0x3 / 0x4 - _0x174e2f(_0x3d6263);
            }
            function _0x4b7710(_0x4b02de) {
                var _0x2550bf, _0x4d039a, _0x13e62c, _0x3adc7f, _0x1c734b;
                var _0x2a8704 = _0x4b02de['length'];
                _0x3adc7f = _0x174e2f(_0x4b02de);
                _0x1c734b = new _0x4291b8(_0x2a8704 * 0x3 / 0x4 - _0x3adc7f);
                _0x4d039a = _0x3adc7f > 0x0 ? _0x2a8704 - 0x4 : _0x2a8704;
                var _0x4c2a8c = 0x0;
                for (_0x2550bf = 0x0; _0x2550bf < _0x4d039a; _0x2550bf += 0x4) {
                    _0x13e62c = _0x4e3000[_0x4b02de['charCodeAt'](_0x2550bf)] << 0x12 | _0x4e3000[_0x4b02de[_0xa6cc('0x1b')](_0x2550bf + 0x1)] << 0xc | _0x4e3000[_0x4b02de['charCodeAt'](_0x2550bf + 0x2)] << 0x6 | _0x4e3000[_0x4b02de[_0xa6cc('0x1b')](_0x2550bf + 0x3)];
                    _0x1c734b[_0x4c2a8c++] = _0x13e62c >> 0x10 & 0xff;
                    _0x1c734b[_0x4c2a8c++] = _0x13e62c >> 0x8 & 0xff;
                    _0x1c734b[_0x4c2a8c++] = _0x13e62c & 0xff;
                }
                if (_0x3adc7f === 0x2) {
                    _0x13e62c = _0x4e3000[_0x4b02de['charCodeAt'](_0x2550bf)] << 0x2 | _0x4e3000[_0x4b02de['charCodeAt'](_0x2550bf + 0x1)] >> 0x4;
                    _0x1c734b[_0x4c2a8c++] = _0x13e62c & 0xff;
                } else {
                    if (_0x3adc7f === 0x1) {
                        _0x13e62c = _0x4e3000[_0x4b02de['charCodeAt'](_0x2550bf)] << 0xa | _0x4e3000[_0x4b02de[_0xa6cc('0x1b')](_0x2550bf + 0x1)] << 0x4 | _0x4e3000[_0x4b02de[_0xa6cc('0x1b')](_0x2550bf + 0x2)] >> 0x2;
                        _0x1c734b[_0x4c2a8c++] = _0x13e62c >> 0x8 & 0xff;
                        _0x1c734b[_0x4c2a8c++] = _0x13e62c & 0xff;
                    }
                }
                return _0x1c734b;
            }
            function _0x4e549b(_0x1bc071) {
                return _0x5473f7[_0x1bc071 >> 0x12 & 0x3f] + _0x5473f7[_0x1bc071 >> 0xc & 0x3f] + _0x5473f7[_0x1bc071 >> 0x6 & 0x3f] + _0x5473f7[_0x1bc071 & 0x3f];
            }
            function _0x52333e(_0xadb06b, _0xd1b2ec, _0x5ab800) {
                var _0x2ad5f6;
                var _0x47feb8 = [];
                for (var _0x2143ef = _0xd1b2ec; _0x2143ef < _0x5ab800; _0x2143ef += 0x3) {
                    _0x2ad5f6 = (_0xadb06b[_0x2143ef] << 0x10) + (_0xadb06b[_0x2143ef + 0x1] << 0x8) + _0xadb06b[_0x2143ef + 0x2];
                    _0x47feb8[_0xa6cc('0x21')](_0x4e549b(_0x2ad5f6));
                }
                return _0x47feb8['join']('');
            }
            function _0x171ea5(_0x1b3422) {
                var _0x490c0a;
                var _0x3105b7 = _0x1b3422[_0xa6cc('0x1f')];
                var _0x255bd0 = _0x3105b7 % 0x3;
                var _0x177114 = '';
                var _0x918fd = [];
                var _0x4118a2 = 0x3fff;
                for (var _0x2282f9 = 0x0, _0x345dc3 = _0x3105b7 - _0x255bd0; _0x2282f9 < _0x345dc3; _0x2282f9 += _0x4118a2) {
                    _0x918fd[_0xa6cc('0x21')](_0x52333e(_0x1b3422, _0x2282f9, _0x2282f9 + _0x4118a2 > _0x345dc3 ? _0x345dc3 : _0x2282f9 + _0x4118a2));
                }
                if (_0x255bd0 === 0x1) {
                    _0x490c0a = _0x1b3422[_0x3105b7 - 0x1];
                    _0x177114 += _0x5473f7[_0x490c0a >> 0x2];
                    _0x177114 += _0x5473f7[_0x490c0a << 0x4 & 0x3f];
                    _0x177114 += '==';
                } else {
                    if (_0x255bd0 === 0x2) {
                        _0x490c0a = (_0x1b3422[_0x3105b7 - 0x2] << 0x8) + _0x1b3422[_0x3105b7 - 0x1];
                        _0x177114 += _0x5473f7[_0x490c0a >> 0xa];
                        _0x177114 += _0x5473f7[_0x490c0a >> 0x4 & 0x3f];
                        _0x177114 += _0x5473f7[_0x490c0a << 0x2 & 0x3f];
                        _0x177114 += '=';
                    }
                }
                _0x918fd[_0xa6cc('0x21')](_0x177114);
                return _0x918fd['join']('');
            }
        }
        , {}]
    }, {}, [])('/');
}));
function SM4_Context() {
    this['mode'] = 0x1;
    this[_0xa6cc('0x1c')] = !![];
    this['sk'] = new Array(0x20);
}
function SM4() {
    this['SM4_ENCRYPT'] = 0x1;
    this[_0xa6cc('0xe')] = 0x0;
    var _0x11a929 = [0xd6, 0x90, 0xe9, 0xfe, 0xcc, 0xe1, 0x3d, 0xb7, 0x16, 0xb6, 0x14, 0xc2, 0x28, 0xfb, 0x2c, 0x5, 0x2b, 0x67, 0x9a, 0x76, 0x2a, 0xbe, 0x4, 0xc3, 0xaa, 0x44, 0x13, 0x26, 0x49, 0x86, 0x6, 0x99, 0x9c, 0x42, 0x50, 0xf4, 0x91, 0xef, 0x98, 0x7a, 0x33, 0x54, 0xb, 0x43, 0xed, 0xcf, 0xac, 0x62, 0xe4, 0xb3, 0x1c, 0xa9, 0xc9, 0x8, 0xe8, 0x95, 0x80, 0xdf, 0x94, 0xfa, 0x75, 0x8f, 0x3f, 0xa6, 0x47, 0x7, 0xa7, 0xfc, 0xf3, 0x73, 0x17, 0xba, 0x83, 0x59, 0x3c, 0x19, 0xe6, 0x85, 0x4f, 0xa8, 0x68, 0x6b, 0x81, 0xb2, 0x71, 0x64, 0xda, 0x8b, 0xf8, 0xeb, 0xf, 0x4b, 0x70, 0x56, 0x9d, 0x35, 0x1e, 0x24, 0xe, 0x5e, 0x63, 0x58, 0xd1, 0xa2, 0x25, 0x22, 0x7c, 0x3b, 0x1, 0x21, 0x78, 0x87, 0xd4, 0x0, 0x46, 0x57, 0x9f, 0xd3, 0x27, 0x52, 0x4c, 0x36, 0x2, 0xe7, 0xa0, 0xc4, 0xc8, 0x9e, 0xea, 0xbf, 0x8a, 0xd2, 0x40, 0xc7, 0x38, 0xb5, 0xa3, 0xf7, 0xf2, 0xce, 0xf9, 0x61, 0x15, 0xa1, 0xe0, 0xae, 0x5d, 0xa4, 0x9b, 0x34, 0x1a, 0x55, 0xad, 0x93, 0x32, 0x30, 0xf5, 0x8c, 0xb1, 0xe3, 0x1d, 0xf6, 0xe2, 0x2e, 0x82, 0x66, 0xca, 0x60, 0xc0, 0x29, 0x23, 0xab, 0xd, 0x53, 0x4e, 0x6f, 0xd5, 0xdb, 0x37, 0x45, 0xde, 0xfd, 0x8e, 0x2f, 0x3, 0xff, 0x6a, 0x72, 0x6d, 0x6c, 0x5b, 0x51, 0x8d, 0x1b, 0xaf, 0x92, 0xbb, 0xdd, 0xbc, 0x7f, 0x11, 0xd9, 0x5c, 0x41, 0x1f, 0x10, 0x5a, 0xd8, 0xa, 0xc1, 0x31, 0x88, 0xa5, 0xcd, 0x7b, 0xbd, 0x2d, 0x74, 0xd0, 0x12, 0xb8, 0xe5, 0xb4, 0xb0, 0x89, 0x69, 0x97, 0x4a, 0xc, 0x96, 0x77, 0x7e, 0x65, 0xb9, 0xf1, 0x9, 0xc5, 0x6e, 0xc6, 0x84, 0x18, 0xf0, 0x7d, 0xec, 0x3a, 0xdc, 0x4d, 0x20, 0x79, 0xee, 0x5f, 0x3e, 0xd7, 0xcb, 0x39, 0x48];
    var _0x7131b2 = [0xa3b1bac6, 0x56aa3350, 0x677d9197, 0xb27022dc];
    var _0x311756 = [0x70e15, 0x1c232a31, 0x383f464d, 0x545b6269, 0x70777e85, 0x8c939aa1, 0xa8afb6bd, 0xc4cbd2d9, 0xe0e7eef5, 0xfc030a11, 0x181f262d, 0x343b4249, 0x50575e65, 0x6c737a81, 0x888f969d, 0xa4abb2b9, 0xc0c7ced5, 0xdce3eaf1, 0xf8ff060d, 0x141b2229, 0x30373e45, 0x4c535a61, 0x686f767d, 0x848b9299, 0xa0a7aeb5, 0xbcc3cad1, 0xd8dfe6ed, 0xf4fb0209, 0x10171e25, 0x2c333a41, 0x484f565d, 0x646b7279];
    this[_0xa6cc('0x18')] = function(_0x47dae5, _0x278e33) {
        return (_0x47dae5[_0x278e33] & 0xff) << 0x18 | (_0x47dae5[_0x278e33 + 0x1] & 0xff) << 0x10 | (_0x47dae5[_0x278e33 + 0x2] & 0xff) << 0x8 | _0x47dae5[_0x278e33 + 0x3] & 0xff & 0xffffffff;
    }
    ;
    this[_0xa6cc('0x4')] = function(_0x14265a, _0x92ff6a, _0x1587a1) {
        var _0x4f9315 = 0xff & _0x14265a >> 0x18;
        var _0x559000 = 0xff & _0x14265a >> 0x10;
        var _0x21f042 = 0xff & _0x14265a >> 0x8;
        var _0x4838ee = 0xff & _0x14265a;
        _0x92ff6a[_0x1587a1] = _0x4f9315 > 0x80 ? _0x4f9315 - 0x100 : _0x4f9315;
        _0x92ff6a[_0x1587a1 + 0x1] = _0x559000 > 0x80 ? _0x559000 - 0x100 : _0x559000;
        _0x92ff6a[_0x1587a1 + 0x2] = _0x21f042 > 0x80 ? _0x21f042 - 0x100 : _0x21f042;
        _0x92ff6a[_0x1587a1 + 0x3] = _0x4838ee > 0x80 ? _0x4838ee - 0x100 : _0x4838ee;
    }
    ;
    this['SHL'] = function(_0x4a0de5, _0x31c8df) {
        return (_0x4a0de5 & 0xffffffff) << _0x31c8df;
    }
    ;
    this['ROTL'] = function(_0x5d7568, _0x44affd) {
        var _0x35cc95 = this[_0xa6cc('0x6')](_0x5d7568, _0x44affd);
        var _0x25522c = _0x5d7568 >> 0x20 - _0x44affd;
        return this[_0xa6cc('0x6')](_0x5d7568, _0x44affd) | _0x5d7568 >> 0x20 - _0x44affd;
    }
    ;
    this['sm4Lt'] = function(_0x4c855f) {
        var _0x553a3c = 0x0;
        var _0x1751cd = 0x0;
        var _0x273f7d = new Array(0x4);
        var _0x5e1885 = new Array(0x4);
        this[_0xa6cc('0x4')](_0x4c855f, _0x273f7d, 0x0);
        _0x5e1885[0x0] = this[_0xa6cc('0x5')](_0x273f7d[0x0]);
        _0x5e1885[0x1] = this[_0xa6cc('0x5')](_0x273f7d[0x1]);
        _0x5e1885[0x2] = this[_0xa6cc('0x5')](_0x273f7d[0x2]);
        _0x5e1885[0x3] = this['sm4Sbox'](_0x273f7d[0x3]);
        _0x553a3c = this['GET_ULONG_BE'](_0x5e1885, 0x0);
        _0x1751cd = _0x553a3c ^ this['ROTL'](_0x553a3c, 0x2) ^ this[_0xa6cc('0x23')](_0x553a3c, 0xa) ^ this[_0xa6cc('0x23')](_0x553a3c, 0x12) ^ this[_0xa6cc('0x23')](_0x553a3c, 0x18);
        return _0x1751cd;
    }
    ;
    this[_0xa6cc('0x14')] = function(_0x51e543, _0x3bd7ee, _0x56c02f, _0x331165, _0x3e44f6) {
        return _0x51e543 ^ this['sm4Lt'](_0x3bd7ee ^ _0x56c02f ^ _0x331165 ^ _0x3e44f6);
    }
    ;
    this['sm4CalciRK'] = function(_0x3ad113) {
        var _0x49f1eb = 0x0;
        var _0x1cc646 = 0x0;
        var _0x1a46e3 = new Array(0x4);
        var _0x3ac4b3 = new Array(0x4);
        this['PUT_ULONG_BE'](_0x3ad113, _0x1a46e3, 0x0);
        _0x3ac4b3[0x0] = this[_0xa6cc('0x5')](_0x1a46e3[0x0]);
        _0x3ac4b3[0x1] = this['sm4Sbox'](_0x1a46e3[0x1]);
        _0x3ac4b3[0x2] = this[_0xa6cc('0x5')](_0x1a46e3[0x2]);
        _0x3ac4b3[0x3] = this[_0xa6cc('0x5')](_0x1a46e3[0x3]);
        _0x49f1eb = this['GET_ULONG_BE'](_0x3ac4b3, 0x0);
        _0x1cc646 = _0x49f1eb ^ this[_0xa6cc('0x23')](_0x49f1eb, 0xd) ^ this[_0xa6cc('0x23')](_0x49f1eb, 0x17);
        return _0x1cc646;
    }
    ;
    this[_0xa6cc('0x5')] = function(_0x17afba) {
        var _0x41eb30 = _0x17afba & 0xff;
        var _0x3b5980 = _0x11a929[_0x41eb30];
        return _0x3b5980 > 0x80 ? _0x3b5980 - 0x100 : _0x3b5980;
    }
    ;
    this[_0xa6cc('0xd')] = function(_0x40c3c8, _0x5083d2) {
        if (_0x40c3c8 == null) {
            alert(_0xa6cc('0x17'));
            return ![];
        }
        if (_0x5083d2 == null || _0x5083d2['length'] != 0x10) {
            alert(_0xa6cc('0xf'));
            return ![];
        }
        _0x40c3c8[_0xa6cc('0x1')] = this['SM4_ENCRYPT'];
        this['sm4_setkey'](_0x40c3c8['sk'], _0x5083d2);
    }
    ;
    this['sm4_setkey'] = function(_0x3f19a2, _0x29c56f) {
        var _0x172552 = new Array(0x4);
        var _0x434477 = new Array(0x24);
        var _0x554038 = 0x0;
        _0x172552[0x0] = this['GET_ULONG_BE'](_0x29c56f, 0x0);
        _0x172552[0x1] = this['GET_ULONG_BE'](_0x29c56f, 0x4);
        _0x172552[0x2] = this['GET_ULONG_BE'](_0x29c56f, 0x8);
        _0x172552[0x3] = this[_0xa6cc('0x18')](_0x29c56f, 0xc);
        _0x434477[0x0] = _0x172552[0x0] ^ _0x7131b2[0x0];
        _0x434477[0x1] = _0x172552[0x1] ^ _0x7131b2[0x1];
        _0x434477[0x2] = _0x172552[0x2] ^ _0x7131b2[0x2];
        _0x434477[0x3] = _0x172552[0x3] ^ _0x7131b2[0x3];
        for (var _0x554038 = 0x0; _0x554038 < 0x20; _0x554038++) {
            _0x434477[_0x554038 + 0x4] = _0x434477[_0x554038] ^ this['sm4CalciRK'](_0x434477[_0x554038 + 0x1] ^ _0x434477[_0x554038 + 0x2] ^ _0x434477[_0x554038 + 0x3] ^ _0x311756[_0x554038]);
            _0x3f19a2[_0x554038] = _0x434477[_0x554038 + 0x4];
        }
    }
    ;
    this[_0xa6cc('0xb')] = function(_0x137220, _0x2ebb52) {
        if (_0x137220 == null) {
            return null;
        }
        var _0x92884a = null;
        if (_0x2ebb52 == this[_0xa6cc('0xc')]) {
            var _0x5bec54 = parseInt(0x10 - _0x137220[_0xa6cc('0x1f')] % 0x10);
            _0x92884a = _0x137220['slice'](0x0);
            for (var _0x3bc707 = 0x0; _0x3bc707 < _0x5bec54; _0x3bc707++) {
                _0x92884a[_0x137220[_0xa6cc('0x1f')] + _0x3bc707] = _0x5bec54;
            }
        } else {
            var _0x5bec54 = _0x137220[_0x137220[_0xa6cc('0x1f')] - 0x1];
            _0x92884a = _0x137220['slice'](0x0, _0x137220[_0xa6cc('0x1f')] - _0x5bec54);
        }
        return _0x92884a;
    }
    ;
    this['sm4_one_round'] = function(_0x3675ae, _0x119664, _0x1cd15f) {
        var _0x35f62c = 0x0;
        var _0x2d2d9d = new Array(0x24);
        _0x2d2d9d[0x0] = this['GET_ULONG_BE'](_0x119664, 0x0);
        _0x2d2d9d[0x1] = this['GET_ULONG_BE'](_0x119664, 0x4);
        _0x2d2d9d[0x2] = this['GET_ULONG_BE'](_0x119664, 0x8);
        _0x2d2d9d[0x3] = this[_0xa6cc('0x18')](_0x119664, 0xc);
        while (_0x35f62c < 0x20) {
            _0x2d2d9d[_0x35f62c + 0x4] = this[_0xa6cc('0x14')](_0x2d2d9d[_0x35f62c], _0x2d2d9d[_0x35f62c + 0x1], _0x2d2d9d[_0x35f62c + 0x2], _0x2d2d9d[_0x35f62c + 0x3], _0x3675ae[_0x35f62c]);
            _0x35f62c++;
        }
        this['PUT_ULONG_BE'](_0x2d2d9d[0x23], _0x1cd15f, 0x0);
        this[_0xa6cc('0x4')](_0x2d2d9d[0x22], _0x1cd15f, 0x4);
        this[_0xa6cc('0x4')](_0x2d2d9d[0x21], _0x1cd15f, 0x8);
        this[_0xa6cc('0x4')](_0x2d2d9d[0x20], _0x1cd15f, 0xc);
    }
    ;
    this['sm4_crypt_ecb'] = function(_0xf6ddeb, _0x587d4a) {
        if (_0x587d4a == null) {
            alert('input\x20is\x20null!');
        }
        if (_0xf6ddeb['isPadding'] && _0xf6ddeb['mode'] == this[_0xa6cc('0xc')]) {
            _0x587d4a = this[_0xa6cc('0xb')](_0x587d4a, this['SM4_ENCRYPT']);
        }
        var _0xc075a3 = 0x0;
        var _0x35ecf0 = _0x587d4a['length'];
        var _0x3bba7b = new Array();
        for (; _0x35ecf0 > 0x0; _0x35ecf0 -= 0x10) {
            var _0x14dac2 = new Array(0x10);
            var _0x55a520 = _0x587d4a[_0xa6cc('0x7')](_0xc075a3 * 0x10, 0x10 * (_0xc075a3 + 0x1));
            this['sm4_one_round'](_0xf6ddeb['sk'], _0x55a520, _0x14dac2);
            _0x3bba7b = _0x3bba7b['concat'](_0x14dac2);
            _0xc075a3++;
        }
        var _0x2c9fe1 = _0x3bba7b;
        if (_0xf6ddeb[_0xa6cc('0x1c')] && _0xf6ddeb[_0xa6cc('0x1')] == this[_0xa6cc('0xe')]) {
            _0x2c9fe1 = this['padding'](_0x2c9fe1, this['SM4_DECRYPT']);
        }
        for (var _0xc075a3 = 0x0; _0xc075a3 < _0x2c9fe1['length']; _0xc075a3++) {
            if (_0x2c9fe1[_0xc075a3] < 0x0) {
                _0x2c9fe1[_0xc075a3] = _0x2c9fe1[_0xc075a3] + 0x100;
            }
        }
        return _0x2c9fe1;
    }
    ;
    this['sm4_crypt_cbc'] = function(_0x2b79fc, _0x55d29e, _0x2b489b) {
        if (_0x55d29e == null || _0x55d29e[_0xa6cc('0x1f')] != 0x10) {
            alert('iv\x20error!');
        }
        if (_0x2b489b == null) {
            alert(_0xa6cc('0x20'));
        }
        if (_0x2b79fc['isPadding'] && _0x2b79fc['mode'] == this[_0xa6cc('0xc')]) {
            _0x2b489b = this['padding'](_0x2b489b, this['SM4_ENCRYPT']);
        }
        var _0x41ef90 = 0x0;
        var _0x33abed = _0x2b489b[_0xa6cc('0x1f')];
        var _0x5a646f = new Array();
        if (_0x2b79fc['mode'] == this['SM4_ENCRYPT']) {
            var _0x3b27bf = 0x0;
            for (; _0x33abed > 0x0; _0x33abed -= 0x10) {
                var _0x193a7e = new Array(0x10);
                var _0x236a28 = new Array(0x10);
                var _0x38ec21 = _0x2b489b['slice'](_0x3b27bf * 0x10, 0x10 * (_0x3b27bf + 0x1));
                for (_0x41ef90 = 0x0; _0x41ef90 < 0x10; _0x41ef90++) {
                    _0x193a7e[_0x41ef90] = _0x38ec21[_0x41ef90] ^ _0x55d29e[_0x41ef90];
                }
                this[_0xa6cc('0x9')](_0x2b79fc['sk'], _0x193a7e, _0x236a28);
                _0x55d29e = _0x236a28[_0xa6cc('0x7')](0x0, 0x10);
                _0x5a646f = _0x5a646f['concat'](_0x236a28);
                _0x3b27bf++;
            }
        } else {
            var _0x1e6ca7 = [];
            var _0x3b27bf = 0x0;
            for (; _0x33abed > 0x0; _0x33abed -= 0x10) {
                var _0x193a7e = new Array(0x10);
                var _0x236a28 = new Array(0x10);
                var _0x38ec21 = _0x2b489b['slice'](_0x3b27bf * 0x10, 0x10 * (_0x3b27bf + 0x1));
                _0x1e6ca7 = _0x38ec21[_0xa6cc('0x7')](0x0, 0x10);
                sm4_one_round(_0x2b79fc['sk'], _0x38ec21, _0x193a7e);
                for (_0x41ef90 = 0x0; _0x41ef90 < 0x10; _0x41ef90++) {
                    _0x236a28[_0x41ef90] = _0x193a7e[_0x41ef90] ^ _0x55d29e[_0x41ef90];
                }
                _0x55d29e = _0x1e6ca7['slice'](0x0, 0x10);
                _0x5a646f = _0x5a646f['concat'](_0x236a28);
                _0x3b27bf++;
            }
        }
        var _0x4a12ef = _0x5a646f;
        if (_0x2b79fc['isPadding'] && _0x2b79fc[_0xa6cc('0x1')] == this[_0xa6cc('0xe')]) {
            _0x4a12ef = this['padding'](_0x4a12ef, this['SM4_DECRYPT']);
        }
        for (var _0x41ef90 = 0x0; _0x41ef90 < _0x4a12ef['length']; _0x41ef90++) {
            if (_0x4a12ef[_0x41ef90] < 0x0) {
                _0x4a12ef[_0x41ef90] = _0x4a12ef[_0x41ef90] + 0x100;
            }
        }
        return _0x4a12ef;
    }
    ;
}
base64js=new base64js

function SM4Util() {
    this[_0xa6cc('0x2')] = '';
    this['iv'] = '';
    this['hexString'] = ![];
    this['encryptData_ECB'] = function(_0x592151) {
        try {
            var _0x2afb4b = new SM4();
            var _0x5b4e4f = new SM4_Context();
            _0x5b4e4f['isPadding'] = !![];
            _0x5b4e4f[_0xa6cc('0x1')] = _0x2afb4b['SM4_ENCRYPT'];
            var _0x1c1590 = stringToByte(this['secretKey']);
            _0x2afb4b[_0xa6cc('0xd')](_0x5b4e4f, _0x1c1590);
            var _0x3d8b7a = _0x2afb4b[_0xa6cc('0x11')](_0x5b4e4f, stringToByte(_0x592151));
            var _0x15b6a4 = base64js.fromByteArray(_0x3d8b7a);
            if (_0x15b6a4 != null && _0x15b6a4[_0xa6cc('0x15')]()['length'] > 0x0) {
                _0x15b6a4['replace'](/(\s*|\t|\r|\n)/g, '');
            }
            return _0x15b6a4;
        } catch (_0x3ac885) {
            console[_0xa6cc('0x16')](_0x3ac885);
            return null;
        }
    }
    ;
    this['encryptData_CBC'] = function(_0x179280) {
        try {
            var _0x3dcd74 = new SM4();
            var _0x568578 = new SM4_Context();
            _0x568578['isPadding'] = !![];
            _0x568578[_0xa6cc('0x1')] = _0x3dcd74['SM4_ENCRYPT'];
            var _0x471688 = stringToByte(this['secretKey']);
            var _0x3368a9 = stringToByte(this['iv']);
            _0x3dcd74[_0xa6cc('0xd')](_0x568578, _0x471688);
            var _0x73f402 = _0x3dcd74[_0xa6cc('0x13')](_0x568578, _0x3368a9, stringToByte(_0x179280));
            var _0x39cbe1 = base64js.fromByteArray(_0x73f402);
            if (_0x39cbe1 != null && _0x39cbe1[_0xa6cc('0x15')]()['length'] > 0x0) {
                _0x39cbe1['replace'](/(\s*|\t|\r|\n)/g, '');
            }
            return _0x39cbe1;
        } catch (_0x2ddeaf) {
            console['error'](_0x2ddeaf);
            return null;
        }
    }
    ;
    stringToByte = function(_0x24849d) {
        var _0x4257e5 = new Array();
        var _0xa143f9, _0x264b9a;
        _0xa143f9 = _0x24849d[_0xa6cc('0x1f')];
        for (var _0x15acd4 = 0x0; _0x15acd4 < _0xa143f9; _0x15acd4++) {
            _0x264b9a = _0x24849d['charCodeAt'](_0x15acd4);
            if (_0x264b9a >= 0x10000 && _0x264b9a <= 0x10ffff) {
                _0x4257e5[_0xa6cc('0x21')](_0x264b9a >> 0x12 & 0x7 | 0xf0);
                _0x4257e5['push'](_0x264b9a >> 0xc & 0x3f | 0x80);
                _0x4257e5['push'](_0x264b9a >> 0x6 & 0x3f | 0x80);
                _0x4257e5[_0xa6cc('0x21')](_0x264b9a & 0x3f | 0x80);
            } else if (_0x264b9a >= 0x800 && _0x264b9a <= 0xffff) {
                _0x4257e5['push'](_0x264b9a >> 0xc & 0xf | 0xe0);
                _0x4257e5['push'](_0x264b9a >> 0x6 & 0x3f | 0x80);
                _0x4257e5[_0xa6cc('0x21')](_0x264b9a & 0x3f | 0x80);
            } else if (_0x264b9a >= 0x80 && _0x264b9a <= 0x7ff) {
                _0x4257e5['push'](_0x264b9a >> 0x6 & 0x1f | 0xc0);
                _0x4257e5['push'](_0x264b9a & 0x3f | 0x80);
            } else {
                _0x4257e5[_0xa6cc('0x21')](_0x264b9a & 0xff);
            }
        }
        return _0x4257e5;
    }
    ;
    byteToString = function(_0x10294f) {
        if (typeof _0x10294f === 'string') {
            return _0x10294f;
        }
        var _0x7c2601 = ''
          , _0x49fc6a = _0x10294f;
        for (var _0xba6647 = 0x0; _0xba6647 < _0x49fc6a[_0xa6cc('0x1f')]; _0xba6647++) {
            var _0xc4f6cc = _0x49fc6a[_0xba6647]['toString'](0x2)
              , _0x27531a = _0xc4f6cc[_0xa6cc('0x19')](/^1+?(?=0)/);
            if (_0x27531a && _0xc4f6cc['length'] == 0x8) {
                var _0x3ddd56 = _0x27531a[0x0]['length'];
                var _0x3c264b = _0x49fc6a[_0xba6647]['toString'](0x2)['slice'](0x7 - _0x3ddd56);
                for (var _0x29a811 = 0x1; _0x29a811 < _0x3ddd56; _0x29a811++) {
                    _0x3c264b += _0x49fc6a[_0x29a811 + _0xba6647][_0xa6cc('0x10')](0x2)[_0xa6cc('0x7')](0x2);
                }
                _0x7c2601 += String[_0xa6cc('0xa')](parseInt(_0x3c264b, 0x2));
                _0xba6647 += _0x3ddd56 - 0x1;
            } else {
                _0x7c2601 += String[_0xa6cc('0xa')](_0x49fc6a[_0xba6647]);
            }
        }
        return _0x7c2601;
    }
    ;
}
;function encString(_0x443a87) {
    var _0x5b1b66 = new SM4Util();
    _0x5b1b66['iv'] = _0xa6cc('0x8');
    _0x5b1b66['secretKey'] = '90ACB357C1AC99D4';
    var _0x565b88 = _0x5b1b66[_0xa6cc('0x1a')](_0x443a87);
    var _0x1c8d86 = _0x565b88['replace'](/\+/g, _0xa6cc('0x22'));
    return _0x1c8d86;
}

function encString(_0x443a87) {
  var _0x5b1b66 = new SM4Util();
  _0x5b1b66["iv"] = _0xa6cc("0x8");
  _0x5b1b66["secretKey"] = "90ACB357C1AC99D4";
  var _0x565b88 = _0x5b1b66[_0xa6cc("0x1a")](_0x443a87);
  var _0x1c8d86 = _0x565b88["replace"](/\+/g, _0xa6cc("0x22"));
  return _0x1c8d86;
}
console.log(encString("654321"));
