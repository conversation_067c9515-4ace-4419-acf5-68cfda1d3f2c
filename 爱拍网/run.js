/*
 * @Date: 2025-08-21 20:27:36
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-21 20:31:17
 * @FilePath: /逆向百例/爱拍网/run.js
 * url https://www.aipai.com/
 */
const axios = require('axios');
const CryptoJS = require('crypto-js');
const forge = require('node-forge'); // 确保已安装 node-forge

// 公钥和登录信息 (请替换为实际值)
const publicKeyPem = `-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDs/S8+O5yCcwypPNAQDmcVGY5U
Ea/iMNDFKcoovLFayhy3Jm/S1L8oYC85Rx8YwWOaQ9Zak0i6eb1AM2JDN7T9+pYb
7mf4fzpE4BbXnAc3OqPwxEsNAsAsMKg6GhVxLu2/bfhrKOZ9Arvf6m/n0bGpfdJh
Idom6iWh5iG4c+z5vwIDAQAB
-----END PUBLIC KEY-----`;
const username = 'test';
const password = '654321';

const userNowTime = Math.floor(Date.now() / 1000);
const keeplogin = 1;
const comouterTime = 1; 
const action = 'loginNew';

// 使用 node-forge 进行 RSA 加密
const publicKey = forge.pki.publicKeyFromPem(publicKeyPem);
let encryptedUsername = forge.util.encode64(publicKey.encrypt(username, 'RSAES-PKCS1-V1_5'));
// 模拟 JSEncrypt 的 replace 操作
encryptedUsername = encryptedUsername.replace(/\s|\n|\r\n/g, "+");

// 使用 CryptoJS 进行 MD5 加密
const encryptedPassword = CryptoJS.MD5(password).toString();

// 准备请求数据
const postData = {
    action: action,
    user: encryptedUsername,
    password: encryptedPassword,
    keeplogin: keeplogin,
    comouterTime: comouterTime,
    userNowTime: userNowTime
};



// 发送 POST 请求
axios.post('https://www.aipai.com/login.php', new URLSearchParams(postData).toString(), {
    headers: {
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Origin': 'https://www.aipai.com',
        'Pragma': 'no-cache',
        'Referer': 'https://www.aipai.com/',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'X-Requested-With': 'XMLHttpRequest',
        'dnt': '1',
        'sec-ch-ua': '"Google Chrome";v="141", "Not?A_Brand";v="8", "Chromium";v="141"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-gpc': '1'
    }
})
.then(response => {
    console.log('Status:', response.status);
    console.log('Response Data:', response.data);
})
.catch(error => {
    console.error('Request Error:', error.message);
    // 可以选择打印更多错误详情，例如 error.response
});
