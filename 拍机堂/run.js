/*
 * @LastEditors: <PERSON>shi <EMAIL>
 * @Date: 2025-08-17 14:06:07
 * @LastEditTime: 2025-08-17 14:13:15
 * @FilePath: /逆向百例/拍机堂/run.js
 * URL https://www.paijitang.com/universal/login?redirectUrl=https://www.paijitang.com/
 */
// encrypt_payload.js
const forge = require('node-forge');

/**
 * 使用 RSA 公钥加密数据，模拟前端 JSEncrypt 行为
 * @param {Object} dataObj - 要加密的 JavaScript 对象 (例如登录凭据)
 * @param {string} publicKeyPem - PEM 格式的 RSA 公钥字符串
 * @returns {string} - 加密后的 Base64 字符串
 */
function encryptPayload(dataObj, publicKeyPem) {
    // 1. 序列化数据为 JSON 字符串 (与 JS 的 JSON.stringify 行为一致)
    //    注意：确保没有额外的空格或换行，与前端保持一致
    const plaintextString = JSON.stringify(dataObj);
    console.log('[Info] Plaintext String to Encrypt:', plaintextString);

    // 2. 确保公钥是完整的 PEM 格式 (包含 BEGIN/END 标记)
    let fullPublicKeyPem = publicKeyPem.trim();
    if (!fullPublicKeyPem.startsWith('-----BEGIN')) {
        // 如果只提供了 Base64 部分，尝试包装它
        fullPublicKeyPem = `-----BEGIN PUBLIC KEY-----\n${fullPublicKeyPem}\n-----END PUBLIC KEY-----`;
        console.log('[Info] Wrapped public key in PEM format.');
    }

    // 3. 将 PEM 格式的公钥加载为 forge 可用的公钥对象
    const publicKey = forge.pki.publicKeyFromPem(fullPublicKeyPem);
    console.log('[Info] Public Key Loaded.');

    // 4. 将明文字符串转换为字节 (UTF-8 编码)
    const bytesToEncrypt = forge.util.encodeUtf8(plaintextString);

    // 5. 使用 RSA 公钥加密
    //    JSEncrypt 默认使用 RSAES-PKCS1-V1_5 填充
    const encryptedBytes = publicKey.encrypt(bytesToEncrypt, 'RSAES-PKCS1-V1_5');

    // 6. 将加密后的字节转换为 Base64 字符串 (与 JSEncrypt 的输出格式一致)
    const encryptedBase64 = forge.util.encode64(encryptedBytes);
    console.log('[Info] Encryption Successful.');

    return encryptedBase64;
}

// --- 配置和执行 ---
const publicKeyBase64FromAnalysis = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCwGdI6FBSptGXZEvVBc1x+xv8mZRmB4lNK44/ZkVQToU3egvCykZZUavhhxYxlow+yBQ+ke+ysaaNHTaHA8Qc8tPVERbfOw6T6lHE+nJkiqCwD7rNmUgM5D+5nIG0HVRS0/WyOP+6KvCgH3kKhoSvw3PIIzDJlmsZifqfSh4LcZwIDAQAB";

// 要加密的登录数据
const loginData = {
    "userName": "test",
    "passWord": "654321"
};

console.log('--- Starting Encryption Process ---');
const encryptedPayload = encryptPayload(loginData, publicKeyBase64FromAnalysis);

console.log('\n--- Generated Encrypted Payload (data-raw) ---');
console.log(encryptedPayload);
console.log('--- End of Payload ---');

console.log('\n--- Encryption Process Finished ---');