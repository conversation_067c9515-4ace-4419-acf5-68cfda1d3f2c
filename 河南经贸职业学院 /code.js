/*
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-08-14 23:30:34
 * @LastEditTime: 2025-08-14 23:35:18
 * @FilePath: /逆向百例/河南经贸职业学院 /code.js
 * url https://auth.henetc.edu.cn/GGSCASServer/login
 */
const CryptoJS = require('crypto-js');
var encryptByDES = function(str) {
    var stime = 1755184516184
      , etime = new Date().getTime();
    if (Math.abs(etime - stime) > 1200000) {
        return 'token=&_wd=&_wf=';
    }
    var UUID = function() {
        var list = [];
        for (var i = 0; i < 8; i++) {
            list.push((((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1));
        }
        return list.join('');
    }
      , _uuid = UUID()
      , b64str = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(str))
      , _json = '{"token":"' + _uuid + '","timeStamp":1755184516184,"data":"' + b64str + '"}'
      , _key = CryptoJS.MD5(encodeURIComponent(_json)).toString()
      , _hashKey = [10, 5, 31, 2, 19, 22, 14, 28]
      , _srcKey = _key.split('')
      , _pds = []
      , item = null
      , _pwd = null
      , data = [];
    for (var i = 0, len = _hashKey.length; i < len; i++) {
        _pds.push(_srcKey[_hashKey[i]]);
    }
    ;_pwd = _pds.join('');
    data.push('token=' + CryptoJS.MD5(_key + _uuid + stime).toString());
    data.push('_wd=' + _key);
    data.push('_wf=' + CryptoJS.DES.encrypt(_json, CryptoJS.enc.Utf8.parse(_pwd), {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7
    }).ciphertext.toString());
    _hashKey = _srcKey = _pds = null;
    return data.join('&');
};
a='"{"username":"test","password":"654321","verifycode":"1222","dlfs":"1,2,3,4","rememberme":false}"'
console.log(encryptByDES(a))