/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-08-14 14:23:49
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-14 15:18:20
 * @FilePath: /逆向百例/好看视频/code.js
 * url https://haokan.baidu.com/
 * 
 */
    const a = require("crypto-js");
     function o(e, t) {
                    for (var r = a, n = "", i = 0; i < e['length']; i++) {
                        var o = e["charCodeAt"](i)
                          , s = t["charCodeAt"](i % t['length']);
                        n += String['fromCharCode'](o ^ s)
                    }
                    return n
                }
function decryptVideo(e) {
  return (
    (e = a["enc"]["Utf8"]["stringify"](a["enc"]["Base64"]["parse"](e))),
    o(e, "guanghui456")
  );
}
res=decryptVideo('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')
console.log(res)