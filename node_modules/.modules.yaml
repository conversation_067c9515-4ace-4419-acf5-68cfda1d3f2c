hoistPattern:
  - '*'
hoistedDependencies:
  asynckit@0.4.0:
    asynckit: private
  base64-js@1.5.1:
    base64-js: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  combined-stream@1.0.8:
    combined-stream: private
  delayed-stream@1.0.0:
    delayed-stream: private
  dunder-proto@1.0.1:
    dunder-proto: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  follow-redirects@1.15.11:
    follow-redirects: private
  form-data@4.0.4:
    form-data: private
  function-bind@1.1.2:
    function-bind: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  gopd@1.2.0:
    gopd: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  jsbn@1.1.0:
    jsbn: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.14.0
pendingBuilds: []
prunedAt: Tue, 12 Aug 2025 07:54:29 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmmirror.com/
skipped: []
storeDir: /root/.local/share/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
