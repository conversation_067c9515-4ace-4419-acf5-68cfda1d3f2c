{"name": "sm-crypto", "version": "0.3.13", "description": "sm-crypto", "main": "src/index.js", "scripts": {"prepublish": "npm run build", "test": "jest ./test/*", "lint": "eslint \"src/**/*.js\" --fix", "build": "npm run lint && webpack"}, "repository": {"type": "git", "url": "git+https://github.com/JuneAndGreen/sm-crypto.git"}, "keywords": ["sm", "js", "crypto"], "jest": {"testEnvironment": "jsdom", "testURL": "https://jest.test"}, "author": "june_01", "license": "MIT", "dependencies": {"jsbn": "^1.1.0"}, "devDependencies": {"babel-core": "^6.26.0", "babel-loader": "^7.1.2", "babel-preset-es2015": "^6.24.1", "jest": "^22.1.4", "webpack": "^3.10.0", "eslint": "^5.3.0", "eslint-config-airbnb-base": "13.1.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-node": "^7.0.1", "eslint-plugin-promise": "^3.8.0"}}