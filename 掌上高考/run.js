/*
 * @Date: 2025-08-18 15:37:12
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-18 15:49:32
 * @FilePath: /逆向百例/掌上高考/run.js
 * url https://www.gaokao.cn/school/search
 */
const CryptoJS=require('crypto-js')
s=function(t){
    return function(t){ return CryptoJS.MD5(t).toString()}
}
function w(t) {
            var {SIGN: t, str: n} = t
              , n = (n = decodeURI(n), 
            CryptoJS.HmacSHA1(CryptoJS.enc.Utf8.parse(n), t));
            t = CryptoJS.enc.Base64.stringify(n).toString();
            return s()(t)
        }
        l='https://api-gaokao.zjzw.cn/apidata/web?platform=2&sign=c697d73f90e942d3ca2cb1dd584d7c71888618e811bc6353a5d6b1c896db316ee51f8ef348db4e0ec0ada1b6a36e3ba39ef90a9c79d29d910837deface794c3f552cf5a3fa373607acd323d00e26b89d4e6859c84fc1feb387502b1b9c4b078a0f69622e2c68fb9dbcc5c32193652ece&uri=v1/user/polling'
         t=({
                SIGN: 'D23ABC@#56',
                str: l.replace(/^\/|https?:\/\/\/?/, "")
            })
            console.log(w(t))