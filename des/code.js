/**
 * des 算法
 * key 8位
 * iv 8位
 * mode CBC
 * padding PKCS7
 * 逆向技巧 搜索关键词 des encrypt enc.utf8  utf8.parse
 */
const CryptoJS = require("crypto-js");

// DES密钥 (8字节)
const key = "mykey123";

// CBC模式加密函数
function encryptDES_CBC(plaintext) {
    // 生成随机IV (8字节，DES块大小)
    const iv = CryptoJS.lib.WordArray.random(8);
    
    // 执行DES-CBC加密
    const encrypted = CryptoJS.DES.encrypt(plaintext, key, {
        mode: CryptoJS.mode.CBC,
        iv: iv,
        padding: CryptoJS.pad.Pkcs7
    });
    
    return {
        ciphertext: encrypted.toString(),
        iv: iv.toString()
    };
}

// CBC模式解密函数
function decryptDES_CBC(encryptedData) {
    // 解析IV
    const iv = CryptoJS.enc.Hex.parse(encryptedData.iv);
    
    // 执行DES-CBC解密
    const decrypted = CryptoJS.DES.decrypt({
        ciphertext: CryptoJS.enc.Base64.parse(encryptedData.ciphertext)
    }, key, {
        mode: CryptoJS.mode.CBC,
        iv: iv,
        padding: CryptoJS.pad.Pkcs7
    });
    
    return decrypted.toString(CryptoJS.enc.Utf8);
}

// 使用示例
const plaintext = "Hello, DES-CBC Mode!";
console.log("原文:", plaintext);

// 加密
const encrypted = encryptDES_CBC(plaintext);
console.log("密文:", encrypted.ciphertext);
console.log("IV:", encrypted.iv);

// 解密
const decrypted = decryptDES_CBC(encrypted);
console.log("解密:", decrypted);
console.log("验证:", plaintext === decrypted);

