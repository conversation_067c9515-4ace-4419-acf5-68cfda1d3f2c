/*
 * @LastEditors: Mishi <EMAIL>
 * @Date: 2025-08-17 15:55:27
 * @LastEditTime: 2025-08-17 16:28:09
 * @FilePath: /逆向百例/智慧养老云平台/run.js
 * url https://ipp12349.cn/hcss/login?from=domain
 */
const CryptoJS = require('crypto-js');

/**
 * 生成loginKey
 * @param {string} username - 用户名
 * @param {string} password - 密码
 * @param {string} verifyCode - 验证码
 * @returns {string} 生成的loginKey
 */
function generateLoginKey(username, password, verifyCode) {
    // 1. 拼接原始字符串：用户名 + 空格 + 密码 + 空格 + 验证码
    const rawStr = `${username} ${password} ${verifyCode}`;
    
    // 2. 定义DES加密密钥（从代码中解析得到）
    const key = CryptoJS.enc.Utf8.parse('S29jJd2j9is2iwjs92ccnj2oxs292Q02dwdwd');
    
    // 3. 执行DES-ECB加密，使用Pkcs7填充
    const encrypted = CryptoJS.DES.encrypt(rawStr, key, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7
    });
    
    // 4. 将加密结果转为Base64字符串
    let base64Str = encrypted.toString();
    
    // 5. 替换处理：+ → %2B，? → %3F
    base64Str = base64Str.replace(/\+/g, '%2B').replace(/\?/g, '%3F');
    
    return base64Str;
}

// 测试：使用示例数据生成loginKey
const username = 'test';
const password = '654321';
const verifyCode = 'ze5v';
const loginKey = generateLoginKey(username, password, verifyCode);

console.log('生成的loginKey:', loginKey);
