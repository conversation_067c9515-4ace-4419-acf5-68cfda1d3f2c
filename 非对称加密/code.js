window=global
const  JSEncrypt  = require('jsencrypt');

// 创建JSEncrypt实例用于生成密钥对
const crypt = new JSEncrypt({default_key_size: 2048});

// 生成RSA密钥对
console.log("=== 生成RSA密钥对 ===");
// const publicKey = crypt.getPublicKey();
const publicKey = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAhPfyPWgEj+1mTJ5t4ytT
6k+zTQQB///U4TNyE7hlrFgVl2JW5ZR+DKHn2l9Qqeh6Oc4y8QH+sZY88Mb0yp7Y
LHcQ9tzYOaIZ7HflCdLZLYB9flRSWizjxgaQIDpTmwup8issfmAcqq2diNgix88k
uVvFIwAUq4us+tL29zGJoNWp2sWtgVA19+biz2y2lVfT5KhTl+dwmxw0LC2nekec
vLJLDuUiTzlYeGFm6r/ubwgZRDETRvCtRJolR2Psn+NoOmdxqJxsIZXC5Nym3J4C
Ee3IN8J5yShOEwllBOW1h2GvUZ7xXW/zCqA3t1423dEljSenmp/v6NhFeia7ZBK7
NQIDAQAB
-----END PUBLIC KEY-----`;
// const privateKey = crypt.getPrivateKey();
const privateKey = `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`;

console.log("公钥:");
console.log(publicKey);
console.log("\n私钥:");
console.log(privateKey);
// 公钥加密函数
function encryptWithPublicKey(text, pubKey) {
    const encrypt = new JSEncrypt();
    encrypt.setPublicKey(pubKey);
    return encrypt.encrypt(text);
}

// 私钥解密函数
function decryptWithPrivateKey(encryptedText, privKey) {
    const decrypt = new JSEncrypt();
    decrypt.setPrivateKey(privKey);
    return decrypt.decrypt(encryptedText);
}

// 使用示例
console.log("\n=== 加密解密示例 ===");
const plaintext = "Hello, RSA非对称加密!";
console.log("原文:", plaintext);

try {
    // 公钥加密
    const encrypted = encryptWithPublicKey(plaintext, publicKey);
    console.log("加密后:", encrypted);

    if (encrypted) {
        // 私钥解密
        const decrypted = decryptWithPrivateKey(encrypted, privateKey);
        console.log("解密后:", decrypted);
        console.log("验证:", plaintext === decrypted);
    } else {
        console.log("加密失败");
    }
} catch (error) {
    console.error("加密解密过程中出现错误:", error.message);
}