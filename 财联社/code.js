

/**
 * url
 * https://www.cls.cn/
 */
window=global
!function(e) {
    function r(r) {
        for (var n, a, i = r[0], c = r[1], l = r[2], p = 0, s = []; p < i.length; p++)
            a = i[p],
            Object.prototype.hasOwnProperty.call(o, a) && o[a] && s.push(o[a][0]),
            o[a] = 0;
        for (n in c)
            Object.prototype.hasOwnProperty.call(c, n) && (e[n] = c[n]);
        for (f && f(r); s.length; )
            s.shift()();
        return u.push.apply(u, l || []),
        t()
    }
    function t() {
        for (var e, r = 0; r < u.length; r++) {
            for (var t = u[r], n = !0, i = 1; i < t.length; i++) {
                var c = t[i];
                0 !== o[c] && (n = !1)
            }
            n && (u.splice(r--, 1),
            e = a(a.s = t[0]))
        }
        return e
    }
    var n = {}
      , o = {
        0: 0
    }
      , u = [];
    function a(r) {
        if (n[r])
            return n[r].exports;
        var t = n[r] = {
            i: r,
            l: !1,
            exports: {}
        }
          , o = !0;
            console.log(r)
            e[r].call(t.exports, t, t.exports, a),
            o = !1
        return t.l = !0,
        t.exports
    }
    a.e = function(e) {
        var r = []
          , t = o[e];
        if (0 !== t)
            if (t)
                r.push(t[2]);
            else {
                var n = new Promise((function(r, n) {
                    t = o[e] = [r, n]
                }
                ));
                r.push(t[2] = n);
                var u, i = document.createElement("script");
                i.charset = "utf-8",
                i.timeout = 120,
                a.nc && i.setAttribute("nonce", a.nc),
                i.src = function(e) {
                    return a.p + "static/chunks/" + ({}[e] || e) + "." + {
                        27: "30c0490d7d68b7b8fe6d",
                        61: "85d901d1b6f349e5a744",
                        62: "065a6f71ea01a907d3d0"
                    }[e] + ".js"
                }(e);
                var c = new Error;
                u = function(r) {
                    i.onerror = i.onload = null,
                    clearTimeout(l);
                    var t = o[e];
                    if (0 !== t) {
                        if (t) {
                            var n = r && ("load" === r.type ? "missing" : r.type)
                              , u = r && r.target && r.target.src;
                            c.message = "Loading chunk " + e + " failed.\n(" + n + ": " + u + ")",
                            c.name = "ChunkLoadError",
                            c.type = n,
                            c.request = u,
                            t[1](c)
                        }
                        o[e] = void 0
                    }
                }
                ;
                var l = setTimeout((function() {
                    u({
                        type: "timeout",
                        target: i
                    })
                }
                ), 12e4);
                i.onerror = i.onload = u,
                document.head.appendChild(i)
            }
        return Promise.all(r)
    }
    ,
    a.m = e,
    a.c = n,
    a.d = function(e, r, t) {
        a.o(e, r) || Object.defineProperty(e, r, {
            enumerable: !0,
            get: t
        })
    }
    ,
    a.r = function(e) {
        "undefined" !== typeof Symbol && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, {
            value: "Module"
        }),
        Object.defineProperty(e, "__esModule", {
            value: !0
        })
    }
    ,
    a.t = function(e, r) {
        if (1 & r && (e = a(e)),
        8 & r)
            return e;
        if (4 & r && "object" === typeof e && e && e.__esModule)
            return e;
        var t = Object.create(null);
        if (a.r(t),
        Object.defineProperty(t, "default", {
            enumerable: !0,
            value: e
        }),
        2 & r && "string" != typeof e)
            for (var n in e)
                a.d(t, n, function(r) {
                    return e[r]
                }
                .bind(null, n));
        return t
    }
    ,
    a.n = function(e) {
        var r = e && e.__esModule ? function() {
            return e.default
        }
        : function() {
            return e
        }
        ;
        return a.d(r, "a", r),
        r
    }
    ,
    a.o = function(e, r) {
        return Object.prototype.hasOwnProperty.call(e, r)
    }
    ,
    a.p = "",
    a.oe = function(e) {
        throw console.error(e),
        e
    }
    ;
    var i = window.webpackJsonp = window.webpackJsonp || []
      , c = i.push.bind(i);
    i.push = r,
    i = i.slice();
    for (var l = 0; l < i.length; l++)
        r(i[l]);
    var f = c;
    // t()
    jzq=a
}({
        KjvB: function(e, t, n) {
        var r = new (n("c4+4"))
          , o = "undefined" !== typeof window ? window : self
          , i = o.crypto || o.msCrypto || {}
          , a = i.subtle || i.webkitSubtle;
        function u(e) {
            return r.digest(e)
        }
        try {
            a.digest({
                name: "sha-1"
            }, new Uint8Array).catch((function() {
                a = !1
            }
            ))
        } catch (s) {
            a = !1
        }
        e.exports = function(e, t) {
            a ? ("string" === typeof e && (e = function(e) {
                for (var t = e.length, n = new Uint8Array(t), r = 0; r < t; r++)
                    n[r] = e.charCodeAt(r);
                return n
            }(e)),
            a.digest({
                name: "sha-1"
            }, e).then((function(e) {
                t(function(e) {
                    for (var t = e.length, n = [], r = 0; r < t; r++) {
                        var o = e[r];
                        n.push((o >>> 4).toString(16)),
                        n.push((15 & o).toString(16))
                    }
                    return n.join("")
                }(new Uint8Array(e)))
            }
            ), (function(n) {
                t(u(e))
            }
            ))) : setTimeout(t, 0, u(e))
        }
        ,
        e.exports.sync = u
    },
    "c4+4": function(e, t, n) {
        var r;
        "undefined" !== typeof self && self,
        r = function() {
            return function(e) {
                var t = {};
                function n(r) {
                    if (t[r])
                        return t[r].exports;
                    var o = t[r] = {
                        i: r,
                        l: !1,
                        exports: {}
                    };
                    return e[r].call(o.exports, o, o.exports, n),
                    o.l = !0,
                    o.exports
                }
                return n.m = e,
                n.c = t,
                n.d = function(e, t, r) {
                    n.o(e, t) || Object.defineProperty(e, t, {
                        configurable: !1,
                        enumerable: !0,
                        get: r
                    })
                }
                ,
                n.n = function(e) {
                    var t = e && e.__esModule ? function() {
                        return e.default
                    }
                    : function() {
                        return e
                    }
                    ;
                    return n.d(t, "a", t),
                    t
                }
                ,
                n.o = function(e, t) {
                    return Object.prototype.hasOwnProperty.call(e, t)
                }
                ,
                n.p = "",
                n(n.s = 3)
            }([function(e, t, n) {
                var r = n(5)
                  , o = n(1)
                  , i = o.toHex
                  , a = o.ceilHeapSize
                  , u = n(6)
                  , s = function(e) {
                    for (e += 9; e % 64 > 0; e += 1)
                        ;
                    return e
                }
                  , c = function(e, t) {
                    var n = new Int32Array(e,t + 320,5)
                      , r = new Int32Array(5)
                      , o = new DataView(r.buffer);
                    return o.setInt32(0, n[0], !1),
                    o.setInt32(4, n[1], !1),
                    o.setInt32(8, n[2], !1),
                    o.setInt32(12, n[3], !1),
                    o.setInt32(16, n[4], !1),
                    r
                }
                  , f = function() {
                    function e(t) {
                        if (function(e, t) {
                            if (!(e instanceof t))
                                throw new TypeError("Cannot call a class as a function")
                        }(this, e),
                        (t = t || 65536) % 64 > 0)
                            throw new Error("Chunk size must be a multiple of 128 bit");
                        this._offset = 0,
                        this._maxChunkLen = t,
                        this._padMaxChunkLen = s(t),
                        this._heap = new ArrayBuffer(a(this._padMaxChunkLen + 320 + 20)),
                        this._h32 = new Int32Array(this._heap),
                        this._h8 = new Int8Array(this._heap),
                        this._core = new r({
                            Int32Array: Int32Array
                        },{},this._heap)
                    }
                    return e.prototype._initState = function(e, t) {
                        this._offset = 0;
                        var n = new Int32Array(e,t + 320,5);
                        n[0] = 1732584193,
                        n[1] = -271733879,
                        n[2] = -1732584194,
                        n[3] = 271733878,
                        n[4] = -1009589776
                    }
                    ,
                    e.prototype._padChunk = function(e, t) {
                        var n = s(e)
                          , r = new Int32Array(this._heap,0,n >> 2);
                        return function(e, t) {
                            var n = new Uint8Array(e.buffer)
                              , r = t % 4
                              , o = t - r;
                            switch (r) {
                            case 0:
                                n[o + 3] = 0;
                            case 1:
                                n[o + 2] = 0;
                            case 2:
                                n[o + 1] = 0;
                            case 3:
                                n[o + 0] = 0
                            }
                            for (var i = 1 + (t >> 2); i < e.length; i++)
                                e[i] = 0
                        }(r, e),
                        function(e, t, n) {
                            e[t >> 2] |= 128 << 24 - (t % 4 << 3),
                            e[14 + (2 + (t >> 2) & -16)] = n / (1 << 29) | 0,
                            e[15 + (2 + (t >> 2) & -16)] = n << 3
                        }(r, e, t),
                        n
                    }
                    ,
                    e.prototype._write = function(e, t, n, r) {
                        u(e, this._h8, this._h32, t, n, r || 0)
                    }
                    ,
                    e.prototype._coreCall = function(e, t, n, r, o) {
                        var i = n;
                        this._write(e, t, n),
                        o && (i = this._padChunk(n, r)),
                        this._core.hash(i, this._padMaxChunkLen)
                    }
                    ,
                    e.prototype.rawDigest = function(e) {
                        var t = e.byteLength || e.length || e.size || 0;
                        this._initState(this._heap, this._padMaxChunkLen);
                        var n = 0
                          , r = this._maxChunkLen;
                        for (n = 0; t > n + r; n += r)
                            this._coreCall(e, n, r, t, !1);
                        return this._coreCall(e, n, t - n, t, !0),
                        c(this._heap, this._padMaxChunkLen)
                    }
                    ,
                    e.prototype.digest = function(e) {
                        return i(this.rawDigest(e).buffer)
                    }
                    ,
                    e.prototype.digestFromString = function(e) {
                        return this.digest(e)
                    }
                    ,
                    e.prototype.digestFromBuffer = function(e) {
                        return this.digest(e)
                    }
                    ,
                    e.prototype.digestFromArrayBuffer = function(e) {
                        return this.digest(e)
                    }
                    ,
                    e.prototype.resetState = function() {
                        return this._initState(this._heap, this._padMaxChunkLen),
                        this
                    }
                    ,
                    e.prototype.append = function(e) {
                        var t = 0
                          , n = e.byteLength || e.length || e.size || 0
                          , r = this._offset % this._maxChunkLen
                          , o = void 0;
                        for (this._offset += n; t < n; )
                            o = Math.min(n - t, this._maxChunkLen - r),
                            this._write(e, t, o, r),
                            t += o,
                            (r += o) === this._maxChunkLen && (this._core.hash(this._maxChunkLen, this._padMaxChunkLen),
                            r = 0);
                        return this
                    }
                    ,
                    e.prototype.getState = function() {
                        var e = void 0;
                        if (this._offset % this._maxChunkLen)
                            e = this._heap.slice(0);
                        else {
                            var t = new Int32Array(this._heap,this._padMaxChunkLen + 320,5);
                            e = t.buffer.slice(t.byteOffset, t.byteOffset + t.byteLength)
                        }
                        return {
                            offset: this._offset,
                            heap: e
                        }
                    }
                    ,
                    e.prototype.setState = function(e) {
                        return this._offset = e.offset,
                        20 === e.heap.byteLength ? new Int32Array(this._heap,this._padMaxChunkLen + 320,5).set(new Int32Array(e.heap)) : this._h32.set(new Int32Array(e.heap)),
                        this
                    }
                    ,
                    e.prototype.rawEnd = function() {
                        var e = this._offset
                          , t = e % this._maxChunkLen
                          , n = this._padChunk(t, e);
                        this._core.hash(n, this._padMaxChunkLen);
                        var r = c(this._heap, this._padMaxChunkLen);
                        return this._initState(this._heap, this._padMaxChunkLen),
                        r
                    }
                    ,
                    e.prototype.end = function() {
                        return i(this.rawEnd().buffer)
                    }
                    ,
                    e
                }();
                e.exports = f,
                e.exports._core = r
            }
            , function(e, t) {
                for (var n = new Array(256), r = 0; r < 256; r++)
                    n[r] = (r < 16 ? "0" : "") + r.toString(16);
                e.exports.toHex = function(e) {
                    for (var t = new Uint8Array(e), r = new Array(e.byteLength), o = 0; o < r.length; o++)
                        r[o] = n[t[o]];
                    return r.join("")
                }
                ,
                e.exports.ceilHeapSize = function(e) {
                    var t = 0;
                    if (e <= 65536)
                        return 65536;
                    if (e < 16777216)
                        for (t = 1; t < e; t <<= 1)
                            ;
                    else
                        for (t = 16777216; t < e; t += 16777216)
                            ;
                    return t
                }
                ,
                e.exports.isDedicatedWorkerScope = function(e) {
                    var t = "WorkerGlobalScope"in e && e instanceof e.WorkerGlobalScope
                      , n = "SharedWorkerGlobalScope"in e && e instanceof e.SharedWorkerGlobalScope
                      , r = "ServiceWorkerGlobalScope"in e && e instanceof e.ServiceWorkerGlobalScope;
                    return t && !n && !r
                }
            }
            , function(e, t, n) {
                e.exports = function() {
                    var e = n(0)
                      , t = function(e, n, r, o, i) {
                        var a = new self.FileReader;
                        a.onloadend = function() {
                            if (a.error)
                                return i(a.error);
                            var u = a.result;
                            n += a.result.byteLength;
                            try {
                                e.append(u)
                            } catch (s) {
                                return void i(s)
                            }
                            n < o.size ? t(e, n, r, o, i) : i(null, e.end())
                        }
                        ,
                        a.readAsArrayBuffer(o.slice(n, n + r))
                    }
                      , r = !0;
                    return self.onmessage = function(n) {
                        if (r) {
                            var o = n.data.data
                              , i = n.data.file
                              , a = n.data.id;
                            if ("undefined" !== typeof a && (i || o)) {
                                var u = n.data.blockSize || 4194304
                                  , s = new e(u);
                                s.resetState();
                                var c = function(e, t) {
                                    e ? self.postMessage({
                                        id: a,
                                        error: e.name
                                    }) : self.postMessage({
                                        id: a,
                                        hash: t
                                    })
                                };
                                o && function(e, t, n) {
                                    try {
                                        n(null, e.digest(t))
                                    } catch (r) {
                                        return n(r)
                                    }
                                }(s, o, c),
                                i && t(s, 0, u, i, c)
                            }
                        }
                    }
                    ,
                    function() {
                        r = !1
                    }
                }
            }
            , function(e, t, n) {
                var r = n(4)
                  , o = n(0)
                  , i = n(7)
                  , a = n(2)
                  , u = n(1).isDedicatedWorkerScope
                  , s = "undefined" !== typeof self && u(self);
                o.disableWorkerBehaviour = s ? a() : function() {}
                ,
                o.createWorker = function() {
                    var e = r(2)
                      , t = e.terminate;
                    return e.terminate = function() {
                        URL.revokeObjectURL(e.objectURL),
                        t.call(e)
                    }
                    ,
                    e
                }
                ,
                o.createHash = i,
                e.exports = o
            }
            , function(e, t, n) {
                function r(e) {
                    var t = {};
                    function n(r) {
                        if (t[r])
                            return t[r].exports;
                        var o = t[r] = {
                            i: r,
                            l: !1,
                            exports: {}
                        };
                        return e[r].call(o.exports, o, o.exports, n),
                        o.l = !0,
                        o.exports
                    }
                    n.m = e,
                    n.c = t,
                    n.i = function(e) {
                        return e
                    }
                    ,
                    n.d = function(e, t, r) {
                        n.o(e, t) || Object.defineProperty(e, t, {
                            configurable: !1,
                            enumerable: !0,
                            get: r
                        })
                    }
                    ,
                    n.r = function(e) {
                        Object.defineProperty(e, "__esModule", {
                            value: !0
                        })
                    }
                    ,
                    n.n = function(e) {
                        var t = e && e.__esModule ? function() {
                            return e.default
                        }
                        : function() {
                            return e
                        }
                        ;
                        return n.d(t, "a", t),
                        t
                    }
                    ,
                    n.o = function(e, t) {
                        return Object.prototype.hasOwnProperty.call(e, t)
                    }
                    ,
                    n.p = "/",
                    n.oe = function(e) {
                        throw console.error(e),
                        e
                    }
                    ;
                    var r = n(n.s = ENTRY_MODULE);
                    return r.default || r
                }
                var o = "[\\.|\\-|\\+|\\w|/|@]+"
                  , i = "\\((/\\*.*?\\*/)?s?.*?(" + o + ").*?\\)";
                function a(e) {
                    return (e + "").replace(/[.?*+^$[\]\\(){}|-]/g, "\\$&")
                }
                function u(e, t, r) {
                    var u = {};
                    u[r] = [];
                    var s = t.toString()
                      , c = s.match(/^function\s?\(\w+,\s*\w+,\s*(\w+)\)/);
                    if (!c)
                        return u;
                    for (var f, p = c[1], l = new RegExp("(\\\\n|\\W)" + a(p) + i,"g"); f = l.exec(s); )
                        "dll-reference" !== f[3] && u[r].push(f[3]);
                    for (l = new RegExp("\\(" + a(p) + '\\("(dll-reference\\s(' + o + '))"\\)\\)' + i,"g"); f = l.exec(s); )
                        e[f[2]] || (u[r].push(f[1]),
                        e[f[2]] = n(f[1]).m),
                        u[f[2]] = u[f[2]] || [],
                        u[f[2]].push(f[4]);
                    return u
                }
                function s(e) {
                    return Object.keys(e).reduce((function(t, n) {
                        return t || e[n].length > 0
                    }
                    ), !1)
                }
                e.exports = function(e, t) {
                    t = t || {};
                    var o = {
                        main: n.m
                    }
                      , i = t.all ? {
                        main: Object.keys(o)
                    } : function(e, t) {
                        for (var n = {
                            main: [t]
                        }, r = {
                            main: []
                        }, o = {
                            main: {}
                        }; s(n); )
                            for (var i = Object.keys(n), a = 0; a < i.length; a++) {
                                var c = i[a]
                                  , f = n[c].pop();
                                if (o[c] = o[c] || {},
                                !o[c][f] && e[c][f]) {
                                    o[c][f] = !0,
                                    r[c] = r[c] || [],
                                    r[c].push(f);
                                    for (var p = u(e, e[c][f], c), l = Object.keys(p), h = 0; h < l.length; h++)
                                        n[l[h]] = n[l[h]] || [],
                                        n[l[h]] = n[l[h]].concat(p[l[h]])
                                }
                            }
                        return r
                    }(o, e)
                      , a = "";
                    Object.keys(i).filter((function(e) {
                        return "main" !== e
                    }
                    )).forEach((function(e) {
                        for (var t = 0; i[e][t]; )
                            t++;
                        i[e].push(t),
                        o[e][t] = "(function(module, exports, __webpack_require__) { module.exports = __webpack_require__; })",
                        a = a + "var " + e + " = (" + r.toString().replace("ENTRY_MODULE", JSON.stringify(t)) + ")({" + i[e].map((function(t) {
                            return JSON.stringify(t) + ": " + o[e][t].toString()
                        }
                        )).join(",") + "});\n"
                    }
                    )),
                    a = a + "(" + r.toString().replace("ENTRY_MODULE", JSON.stringify(e)) + ")({" + i.main.map((function(e) {
                        return JSON.stringify(e) + ": " + o.main[e].toString()
                    }
                    )).join(",") + "})(self);";
                    var c = new window.Blob([a],{
                        type: "text/javascript"
                    });
                    if (t.bare)
                        return c;
                    var f = (window.URL || window.webkitURL || window.mozURL || window.msURL).createObjectURL(c)
                      , p = new window.Worker(f);
                    return p.objectURL = f,
                    p
                }
            }
            , function(e, t) {
                e.exports = function(e, t, n) {
                    "use asm";
                    var r = new e.Int32Array(n);
                    function o(e, t) {
                        e = e | 0;
                        t = t | 0;
                        var n = 0
                          , o = 0
                          , i = 0
                          , a = 0
                          , u = 0
                          , s = 0
                          , c = 0
                          , f = 0
                          , p = 0
                          , l = 0
                          , h = 0
                          , d = 0
                          , y = 0
                          , v = 0;
                        i = r[t + 320 >> 2] | 0;
                        u = r[t + 324 >> 2] | 0;
                        c = r[t + 328 >> 2] | 0;
                        p = r[t + 332 >> 2] | 0;
                        h = r[t + 336 >> 2] | 0;
                        for (n = 0; (n | 0) < (e | 0); n = n + 64 | 0) {
                            a = i;
                            s = u;
                            f = c;
                            l = p;
                            d = h;
                            for (o = 0; (o | 0) < 64; o = o + 4 | 0) {
                                v = r[n + o >> 2] | 0;
                                y = ((i << 5 | i >>> 27) + (u & c | ~u & p) | 0) + ((v + h | 0) + 1518500249 | 0) | 0;
                                h = p;
                                p = c;
                                c = u << 30 | u >>> 2;
                                u = i;
                                i = y;
                                r[e + o >> 2] = v
                            }
                            for (o = e + 64 | 0; (o | 0) < (e + 80 | 0); o = o + 4 | 0) {
                                v = (r[o - 12 >> 2] ^ r[o - 32 >> 2] ^ r[o - 56 >> 2] ^ r[o - 64 >> 2]) << 1 | (r[o - 12 >> 2] ^ r[o - 32 >> 2] ^ r[o - 56 >> 2] ^ r[o - 64 >> 2]) >>> 31;
                                y = ((i << 5 | i >>> 27) + (u & c | ~u & p) | 0) + ((v + h | 0) + 1518500249 | 0) | 0;
                                h = p;
                                p = c;
                                c = u << 30 | u >>> 2;
                                u = i;
                                i = y;
                                r[o >> 2] = v
                            }
                            for (o = e + 80 | 0; (o | 0) < (e + 160 | 0); o = o + 4 | 0) {
                                v = (r[o - 12 >> 2] ^ r[o - 32 >> 2] ^ r[o - 56 >> 2] ^ r[o - 64 >> 2]) << 1 | (r[o - 12 >> 2] ^ r[o - 32 >> 2] ^ r[o - 56 >> 2] ^ r[o - 64 >> 2]) >>> 31;
                                y = ((i << 5 | i >>> 27) + (u ^ c ^ p) | 0) + ((v + h | 0) + 1859775393 | 0) | 0;
                                h = p;
                                p = c;
                                c = u << 30 | u >>> 2;
                                u = i;
                                i = y;
                                r[o >> 2] = v
                            }
                            for (o = e + 160 | 0; (o | 0) < (e + 240 | 0); o = o + 4 | 0) {
                                v = (r[o - 12 >> 2] ^ r[o - 32 >> 2] ^ r[o - 56 >> 2] ^ r[o - 64 >> 2]) << 1 | (r[o - 12 >> 2] ^ r[o - 32 >> 2] ^ r[o - 56 >> 2] ^ r[o - 64 >> 2]) >>> 31;
                                y = ((i << 5 | i >>> 27) + (u & c | u & p | c & p) | 0) + ((v + h | 0) - 1894007588 | 0) | 0;
                                h = p;
                                p = c;
                                c = u << 30 | u >>> 2;
                                u = i;
                                i = y;
                                r[o >> 2] = v
                            }
                            for (o = e + 240 | 0; (o | 0) < (e + 320 | 0); o = o + 4 | 0) {
                                v = (r[o - 12 >> 2] ^ r[o - 32 >> 2] ^ r[o - 56 >> 2] ^ r[o - 64 >> 2]) << 1 | (r[o - 12 >> 2] ^ r[o - 32 >> 2] ^ r[o - 56 >> 2] ^ r[o - 64 >> 2]) >>> 31;
                                y = ((i << 5 | i >>> 27) + (u ^ c ^ p) | 0) + ((v + h | 0) - 899497514 | 0) | 0;
                                h = p;
                                p = c;
                                c = u << 30 | u >>> 2;
                                u = i;
                                i = y;
                                r[o >> 2] = v
                            }
                            i = i + a | 0;
                            u = u + s | 0;
                            c = c + f | 0;
                            p = p + l | 0;
                            h = h + d | 0
                        }
                        r[t + 320 >> 2] = i;
                        r[t + 324 >> 2] = u;
                        r[t + 328 >> 2] = c;
                        r[t + 332 >> 2] = p;
                        r[t + 336 >> 2] = h
                    }
                    return {
                        hash: o
                    }
                }
            }
            , function(e, t) {
                var n = this
                  , r = void 0;
                "undefined" !== typeof self && "undefined" !== typeof self.FileReaderSync && (r = new self.FileReaderSync);
                var o = function(e, t, n, r, o, i) {
                    var a = void 0
                      , u = i % 4
                      , s = (o + u) % 4
                      , c = o - s;
                    switch (u) {
                    case 0:
                        t[i] = e[r + 3];
                    case 1:
                        t[i + 1 - (u << 1) | 0] = e[r + 2];
                    case 2:
                        t[i + 2 - (u << 1) | 0] = e[r + 1];
                    case 3:
                        t[i + 3 - (u << 1) | 0] = e[r]
                    }
                    if (!(o < s + (4 - u))) {
                        for (a = 4 - u; a < c; a = a + 4 | 0)
                            n[i + a >> 2 | 0] = e[r + a] << 24 | e[r + a + 1] << 16 | e[r + a + 2] << 8 | e[r + a + 3];
                        switch (s) {
                        case 3:
                            t[i + c + 1 | 0] = e[r + c + 2];
                        case 2:
                            t[i + c + 2 | 0] = e[r + c + 1];
                        case 1:
                            t[i + c + 3 | 0] = e[r + c]
                        }
                    }
                };
                e.exports = function(e, t, i, a, u, s) {
                    if ("string" === typeof e)
                        return function(e, t, n, r, o, i) {
                            var a = void 0
                              , u = i % 4
                              , s = (o + u) % 4
                              , c = o - s;
                            switch (u) {
                            case 0:
                                t[i] = e.charCodeAt(r + 3);
                            case 1:
                                t[i + 1 - (u << 1) | 0] = e.charCodeAt(r + 2);
                            case 2:
                                t[i + 2 - (u << 1) | 0] = e.charCodeAt(r + 1);
                            case 3:
                                t[i + 3 - (u << 1) | 0] = e.charCodeAt(r)
                            }
                            if (!(o < s + (4 - u))) {
                                for (a = 4 - u; a < c; a = a + 4 | 0)
                                    n[i + a >> 2] = e.charCodeAt(r + a) << 24 | e.charCodeAt(r + a + 1) << 16 | e.charCodeAt(r + a + 2) << 8 | e.charCodeAt(r + a + 3);
                                switch (s) {
                                case 3:
                                    t[i + c + 1 | 0] = e.charCodeAt(r + c + 2);
                                case 2:
                                    t[i + c + 2 | 0] = e.charCodeAt(r + c + 1);
                                case 1:
                                    t[i + c + 3 | 0] = e.charCodeAt(r + c)
                                }
                            }
                        }(e, t, i, a, u, s);
                    if (e instanceof Array)
                        return o(e, t, i, a, u, s);
                    if (n && n.Buffer && n.Buffer.isBuffer(e))
                        return o(e, t, i, a, u, s);
                    if (e instanceof ArrayBuffer)
                        return o(new Uint8Array(e), t, i, a, u, s);
                    if (e.buffer instanceof ArrayBuffer)
                        return o(new Uint8Array(e.buffer,e.byteOffset,e.byteLength), t, i, a, u, s);
                    if (e instanceof Blob)
                        return function(e, t, n, o, i, a) {
                            var u = void 0
                              , s = a % 4
                              , c = (i + s) % 4
                              , f = i - c
                              , p = new Uint8Array(r.readAsArrayBuffer(e.slice(o, o + i)));
                            switch (s) {
                            case 0:
                                t[a] = p[3];
                            case 1:
                                t[a + 1 - (s << 1) | 0] = p[2];
                            case 2:
                                t[a + 2 - (s << 1) | 0] = p[1];
                            case 3:
                                t[a + 3 - (s << 1) | 0] = p[0]
                            }
                            if (!(i < c + (4 - s))) {
                                for (u = 4 - s; u < f; u = u + 4 | 0)
                                    n[a + u >> 2 | 0] = p[u] << 24 | p[u + 1] << 16 | p[u + 2] << 8 | p[u + 3];
                                switch (c) {
                                case 3:
                                    t[a + f + 1 | 0] = p[f + 2];
                                case 2:
                                    t[a + f + 2 | 0] = p[f + 1];
                                case 1:
                                    t[a + f + 3 | 0] = p[f]
                                }
                            }
                        }(e, t, i, a, u, s);
                    throw new Error("Unsupported data type.")
                }
            }
            , function(e, t, n) {
                var r = function() {
                    function e(e, t) {
                        for (var n = 0; n < t.length; n++) {
                            var r = t[n];
                            r.enumerable = r.enumerable || !1,
                            r.configurable = !0,
                            "value"in r && (r.writable = !0),
                            Object.defineProperty(e, r.key, r)
                        }
                    }
                    return function(t, n, r) {
                        return n && e(t.prototype, n),
                        r && e(t, r),
                        t
                    }
                }()
                  , o = n(0)
                  , i = n(1).toHex
                  , a = function() {
                    function e() {
                        !function(e, t) {
                            if (!(e instanceof t))
                                throw new TypeError("Cannot call a class as a function")
                        }(this, e),
                        this._rusha = new o,
                        this._rusha.resetState()
                    }
                    return e.prototype.update = function(e) {
                        return this._rusha.append(e),
                        this
                    }
                    ,
                    e.prototype.digest = function(e) {
                        var t = this._rusha.rawEnd().buffer;
                        if (!e)
                            return t;
                        if ("hex" === e)
                            return i(t);
                        throw new Error("unsupported digest encoding")
                    }
                    ,
                    r(e, [{
                        key: "state",
                        get: function() {
                            return this._rusha.getState()
                        },
                        set: function(e) {
                            this._rusha.setState(e)
                        }
                    }]),
                    e
                }();
                e.exports = function() {
                    return new a
                }
            }
            ])
        }
        ,
        e.exports = r()
    },
    aCH8: function(e, t, n) {
        !function() {
            var t = n("ANhw")
              , r = n("mmNF").utf8
              , o = n("g0l/")
              , i = n("mmNF").bin
              , a = function(e, n) {
                e.constructor == String ? e = n && "binary" === n.encoding ? i.stringToBytes(e) : r.stringToBytes(e) : o(e) ? e = Array.prototype.slice.call(e, 0) : Array.isArray(e) || (e = e.toString());
                for (var u = t.bytesToWords(e), s = 8 * e.length, c = 1732584193, f = -271733879, p = -1732584194, l = 271733878, h = 0; h < u.length; h++)
                    u[h] = 16711935 & (u[h] << 8 | u[h] >>> 24) | 4278255360 & (u[h] << 24 | u[h] >>> 8);
                u[s >>> 5] |= 128 << s % 32,
                u[14 + (s + 64 >>> 9 << 4)] = s;
                var d = a._ff
                  , y = a._gg
                  , v = a._hh
                  , m = a._ii;
                for (h = 0; h < u.length; h += 16) {
                    var g = c
                      , w = f
                      , b = p
                      , x = l;
                    c = d(c, f, p, l, u[h + 0], 7, -680876936),
                    l = d(l, c, f, p, u[h + 1], 12, -389564586),
                    p = d(p, l, c, f, u[h + 2], 17, 606105819),
                    f = d(f, p, l, c, u[h + 3], 22, -1044525330),
                    c = d(c, f, p, l, u[h + 4], 7, -176418897),
                    l = d(l, c, f, p, u[h + 5], 12, 1200080426),
                    p = d(p, l, c, f, u[h + 6], 17, -1473231341),
                    f = d(f, p, l, c, u[h + 7], 22, -45705983),
                    c = d(c, f, p, l, u[h + 8], 7, 1770035416),
                    l = d(l, c, f, p, u[h + 9], 12, -1958414417),
                    p = d(p, l, c, f, u[h + 10], 17, -42063),
                    f = d(f, p, l, c, u[h + 11], 22, -1990404162),
                    c = d(c, f, p, l, u[h + 12], 7, 1804603682),
                    l = d(l, c, f, p, u[h + 13], 12, -40341101),
                    p = d(p, l, c, f, u[h + 14], 17, -1502002290),
                    c = y(c, f = d(f, p, l, c, u[h + 15], 22, 1236535329), p, l, u[h + 1], 5, -165796510),
                    l = y(l, c, f, p, u[h + 6], 9, -1069501632),
                    p = y(p, l, c, f, u[h + 11], 14, 643717713),
                    f = y(f, p, l, c, u[h + 0], 20, -373897302),
                    c = y(c, f, p, l, u[h + 5], 5, -701558691),
                    l = y(l, c, f, p, u[h + 10], 9, 38016083),
                    p = y(p, l, c, f, u[h + 15], 14, -660478335),
                    f = y(f, p, l, c, u[h + 4], 20, -405537848),
                    c = y(c, f, p, l, u[h + 9], 5, 568446438),
                    l = y(l, c, f, p, u[h + 14], 9, -1019803690),
                    p = y(p, l, c, f, u[h + 3], 14, -187363961),
                    f = y(f, p, l, c, u[h + 8], 20, 1163531501),
                    c = y(c, f, p, l, u[h + 13], 5, -1444681467),
                    l = y(l, c, f, p, u[h + 2], 9, -51403784),
                    p = y(p, l, c, f, u[h + 7], 14, 1735328473),
                    c = v(c, f = y(f, p, l, c, u[h + 12], 20, -1926607734), p, l, u[h + 5], 4, -378558),
                    l = v(l, c, f, p, u[h + 8], 11, -2022574463),
                    p = v(p, l, c, f, u[h + 11], 16, 1839030562),
                    f = v(f, p, l, c, u[h + 14], 23, -35309556),
                    c = v(c, f, p, l, u[h + 1], 4, -1530992060),
                    l = v(l, c, f, p, u[h + 4], 11, 1272893353),
                    p = v(p, l, c, f, u[h + 7], 16, -155497632),
                    f = v(f, p, l, c, u[h + 10], 23, -1094730640),
                    c = v(c, f, p, l, u[h + 13], 4, 681279174),
                    l = v(l, c, f, p, u[h + 0], 11, -358537222),
                    p = v(p, l, c, f, u[h + 3], 16, -722521979),
                    f = v(f, p, l, c, u[h + 6], 23, 76029189),
                    c = v(c, f, p, l, u[h + 9], 4, -640364487),
                    l = v(l, c, f, p, u[h + 12], 11, -421815835),
                    p = v(p, l, c, f, u[h + 15], 16, 530742520),
                    c = m(c, f = v(f, p, l, c, u[h + 2], 23, -995338651), p, l, u[h + 0], 6, -198630844),
                    l = m(l, c, f, p, u[h + 7], 10, 1126891415),
                    p = m(p, l, c, f, u[h + 14], 15, -1416354905),
                    f = m(f, p, l, c, u[h + 5], 21, -57434055),
                    c = m(c, f, p, l, u[h + 12], 6, 1700485571),
                    l = m(l, c, f, p, u[h + 3], 10, -1894986606),
                    p = m(p, l, c, f, u[h + 10], 15, -1051523),
                    f = m(f, p, l, c, u[h + 1], 21, -2054922799),
                    c = m(c, f, p, l, u[h + 8], 6, 1873313359),
                    l = m(l, c, f, p, u[h + 15], 10, -30611744),
                    p = m(p, l, c, f, u[h + 6], 15, -1560198380),
                    f = m(f, p, l, c, u[h + 13], 21, 1309151649),
                    c = m(c, f, p, l, u[h + 4], 6, -145523070),
                    l = m(l, c, f, p, u[h + 11], 10, -1120210379),
                    p = m(p, l, c, f, u[h + 2], 15, 718787259),
                    f = m(f, p, l, c, u[h + 9], 21, -343485551),
                    c = c + g >>> 0,
                    f = f + w >>> 0,
                    p = p + b >>> 0,
                    l = l + x >>> 0
                }
                return t.endian([c, f, p, l])
            };
            a._ff = function(e, t, n, r, o, i, a) {
                var u = e + (t & n | ~t & r) + (o >>> 0) + a;
                return (u << i | u >>> 32 - i) + t
            }
            ,
            a._gg = function(e, t, n, r, o, i, a) {
                var u = e + (t & r | n & ~r) + (o >>> 0) + a;
                return (u << i | u >>> 32 - i) + t
            }
            ,
            a._hh = function(e, t, n, r, o, i, a) {
                var u = e + (t ^ n ^ r) + (o >>> 0) + a;
                return (u << i | u >>> 32 - i) + t
            }
            ,
            a._ii = function(e, t, n, r, o, i, a) {
                var u = e + (n ^ (t | ~r)) + (o >>> 0) + a;
                return (u << i | u >>> 32 - i) + t
            }
            ,
            a._blocksize = 16,
            a._digestsize = 16,
            e.exports = function(e, n) {
                if (void 0 === e || null === e)
                    throw new Error("Illegal argument " + e);
                var r = t.wordsToBytes(a(e, n));
                return n && n.asBytes ? r : n && n.asString ? i.bytesToString(r) : t.bytesToHex(r)
            }
        }()
    },
    ANhw: function(e, t) {
        !function() {
            var t = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"
              , n = {
                rotl: function(e, t) {
                    return e << t | e >>> 32 - t
                },
                rotr: function(e, t) {
                    return e << 32 - t | e >>> t
                },
                endian: function(e) {
                    if (e.constructor == Number)
                        return 16711935 & n.rotl(e, 8) | 4278255360 & n.rotl(e, 24);
                    for (var t = 0; t < e.length; t++)
                        e[t] = n.endian(e[t]);
                    return e
                },
                randomBytes: function(e) {
                    for (var t = []; e > 0; e--)
                        t.push(Math.floor(256 * Math.random()));
                    return t
                },
                bytesToWords: function(e) {
                    for (var t = [], n = 0, r = 0; n < e.length; n++,
                    r += 8)
                        t[r >>> 5] |= e[n] << 24 - r % 32;
                    return t
                },
                wordsToBytes: function(e) {
                    for (var t = [], n = 0; n < 32 * e.length; n += 8)
                        t.push(e[n >>> 5] >>> 24 - n % 32 & 255);
                    return t
                },
                bytesToHex: function(e) {
                    for (var t = [], n = 0; n < e.length; n++)
                        t.push((e[n] >>> 4).toString(16)),
                        t.push((15 & e[n]).toString(16));
                    return t.join("")
                },
                hexToBytes: function(e) {
                    for (var t = [], n = 0; n < e.length; n += 2)
                        t.push(parseInt(e.substr(n, 2), 16));
                    return t
                },
                bytesToBase64: function(e) {
                    for (var n = [], r = 0; r < e.length; r += 3)
                        for (var o = e[r] << 16 | e[r + 1] << 8 | e[r + 2], i = 0; i < 4; i++)
                            8 * r + 6 * i <= 8 * e.length ? n.push(t.charAt(o >>> 6 * (3 - i) & 63)) : n.push("=");
                    return n.join("")
                },
                base64ToBytes: function(e) {
                    e = e.replace(/[^A-Z0-9+\/]/gi, "");
                    for (var n = [], r = 0, o = 0; r < e.length; o = ++r % 4)
                        0 != o && n.push((t.indexOf(e.charAt(r - 1)) & Math.pow(2, -2 * o + 8) - 1) << 2 * o | t.indexOf(e.charAt(r)) >>> 6 - 2 * o);
                    return n
                }
            };
            e.exports = n
        }()
    },
    mmNF: function(e, t) {
        var n = {
            utf8: {
                stringToBytes: function(e) {
                    return n.bin.stringToBytes(unescape(encodeURIComponent(e)))
                },
                bytesToString: function(e) {
                    return decodeURIComponent(escape(n.bin.bytesToString(e)))
                }
            },
            bin: {
                stringToBytes: function(e) {
                    for (var t = [], n = 0; n < e.length; n++)
                        t.push(255 & e.charCodeAt(n));
                    return t
                },
                bytesToString: function(e) {
                    for (var t = [], n = 0; n < e.length; n++)
                        t.push(String.fromCharCode(e[n]));
                    return t.join("")
                }
            }
        };
        e.exports = n
    },
    "g0l/": function(e, t) {
        function n(e) {
            return !!e.constructor && "function" === typeof e.constructor.isBuffer && e.constructor.isBuffer(e)
        }
        e.exports = function(e) {
            return null != e && (n(e) || function(e) {
                return "function" === typeof e.readFloatLE && "function" === typeof e.slice && n(e.slice(0, 0))
            }(e) || !!e._isBuffer)
        }
    },

W2Yj: function(e, t, n) {
        var r = n("KjvB")
          , o = n("aCH8")
          , i = !0;
        function a(e) {
            return String(e)
        }
        function u(e) {
            return Object.keys(e).sort()
        }
        function s(e) {
            return e.filter((function(e) {
                return e
            }
            )).join("&")
        }
        function c(e, t) {
            var n = typeof t
              , r = null;
            return t === r ? r = i ? r : "".concat(a(e), "=").concat(r) : /string|number|boolean/.test(n) ? r = "".concat(a(e), "=").concat(a(t)) : Array.isArray(t) ? r = function(e, t) {
                return t.length ? s(t.map((function(t, n) {
                    return c("".concat(e, "[").concat(n, "]"), t)
                }
                ))) : a("".concat(e, "[]"))
            }(e, t) : "object" === n && (r = function(e, t) {
                return s(u(t).map((function(n) {
                    return c("".concat(e, "[").concat(n, "]"), t[n])
                }
                )))
            }(e, t)),
            r
        }
        e.exports = function(e) {
            var t = e && s(u(e).map((function(t) {
                return c(t, e[t])
            }
            )));
            return t = r.sync(t),
            t = o(t)
        }
    }
});




r={
    "rn": 20,
    "lastTime": 1755011441,
    "subscribedColumnIds": "",
    "hasFirstVipArticle": "1",
    "os": "web",
    "sv": "8.4.6",
    "app": "CailianpressWeb"
}
let p=jzq('W2Yj');
let sign=p(r)
console.log(sign);