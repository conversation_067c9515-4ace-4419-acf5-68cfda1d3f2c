/*
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-08-14 18:48:38
 * @LastEditTime: 2025-08-14 19:14:06
 * @FilePath: /逆向百例/党政机关会议定点场所管理信息系统/code.js
 * url http://meeting.mof.gov.cn/
 */
eval(require('fs').readFileSync(require.resolve('./loader'), 'utf8'));

function run(dpid) {
    setMaxDigits(130);
    key = new RSAKeyPair("10001", "", "a2e32bebda89210ec21122f886a3182e4d5467da6079c0d476fb62c22bc13865e8a6d7d0eef06a521034c2286c8c8abd8a158417e2d2809cbdb0dbaf8b2a9ccba9c6497c1444aba6651c911504db62da8c3f21e57555f8b55fe0bc88f957e919ac305b24ef705f96ff17337cacc9ecd0acd135e8d0d684595c82a2af9af92f7b");
     return encryptedString(key, encodeURIComponent(dpid));

}
console.log(run('03e0135a-de47-46e6-91b2-958b4c1b5434'))