/*
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-08-16 18:29:51
 * @LastEditTime: 2025-08-16 18:37:04
 * @FilePath: /逆向百例/喜号/code.js
 * url https://g.cc9191.com/#/gamelist/1755338819127338?mode=0&gameid=1002416
 * 
 */
const CryptoJS = require("crypto-js");
t = '{"id":"1755338819127338"}';
e = "1755340010000";
de = function (e) {
  for (var t = "", n = CryptoJS.MD5(e).toString(), r = 0; r < n.length; r++)
    r % 2 !== 0 && (t += n[r - 1]);
  return t;
};

n = CryptoJS.MD5("".concat(de(t)).concat(de(e))).toString();
console.log(n)
