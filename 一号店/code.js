/*
 * @LastEditors: Mishi <EMAIL>
 * @Date: 2025-08-19 21:53:26
 * @LastEditTime: 2025-08-19 22:05:31
 * @FilePath: /逆向百例/一号店/code.js
 * url https://passport.yhd.com/passport/login_input.do
 */
eval(require('fs').readFileSync(require.resolve('./loader'), 'utf8'));

function jiami(v){
    pubkey='MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDXQG8rnxhslm+2f7Epu3bB0inrnCaTHhUQCYE+2X+qWQgcpn+Hvwyks3A67mvkIcyvV0ED3HFDf+ANoMWV1Ex56dKqOmSUmjrk7s5cjQeiIsxX7Q3hSzO61/kLpKNH+NE6iAPpm96Fg15rCjbm+5rR96DhLNG7zt2JgOd2o1wXkQIDAQAB'
    var i = new JSEncryptExports.JSEncrypt();
    i.setPublicKey(pubkey);
    return i.encrypt(v);
}
    console.log(jiami('654321'))