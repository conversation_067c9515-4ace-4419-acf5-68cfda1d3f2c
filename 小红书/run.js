/*
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-08-20 15:35:41
 * @LastEditTime: 2025-08-20 17:11:44
 * @FilePath: /逆向百例/小红书/run.js
 */
require('./env')
require('./loader')
const CryptoJS = require('crypto-js')

// 实现 p.xE (base64编码)、p.lz (UTF-8编码) 和 p.Pu (MD5哈希)
var p = {
    xE: function(data) {
        return CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(data));
    },
    lz: function(data) {
        return CryptoJS.enc.Utf8.parse(data).toString();
    },
    Pu: function(data) {
        return CryptoJS.MD5(data).toString();
    }
};

// 配置对象 u
var u = {
    "i8": "4.2.5",
    "mj": "xsecplatform"
};

// 类型检测函数 h._
var h = {
    _: function(t) {
        return t && "undefined" != typeof Symbol && t.constructor === Symbol ? "symbol" : typeof t;
    }
};
x='/api/sns/web/v1/feed'
f={
    "source_note_id": "68928ba10000000023021ad7",
    "image_formats": [
        "jpg",
        "webp",
        "avif"
    ],
    "extra": {
        "need_body_topic": "1"
    },
    "xsec_source": "pc_feed",
    "xsec_token": "AB7CHX7WKt1bhFR_Ib1g3yuacPhM4bAk6fNrnjsHL3qI8="
}
        function seccore_signv2(e, a) {
            var r = window.toString
              , c = e;
            "[object Object]" === r.call(a) || "[object Array]" === r.call(a) || (void 0 === a ? "undefined" : (0,
            h._)(a)) === "object" && null !== a ? c += JSON.stringify(a) : "string" == typeof a && (c += a);
            var d = (0,
            p.Pu)([c].join(""))
              , f = window.mnsv2(c, d)
              , s = {
                x0: u.i8,
                x1: "xhs-pc-web",
                x2: window[u.mj] || "PC",
                x3: f,
                x4: a ? void 0 === a ? "undefined" : (0,
                h._)(a) : ""
            };
            return "XYS_" + (0,
            p.xE)((0,
            p.lz)(JSON.stringify(s)))
        }

// 定义 v 函数指向 seccore_signv2
var v = seccore_signv2;

xs=v(x, f)
