const CryptoJS=require('crypto-js')
Bo=(t)=>{
    return CryptoJS.MD5(t).toString();
}
   rS = () => ["a", "0", "b", "9", "9", "0", "d", "c", "2", "8", "e", "3", "1", "4", "9", "a", "0", "4", "7", "2", "e", "2", "f", "a", "8", "2", "1", "9", "9", "7", "0", "c"].join("")
iS = (e, t, n) => {
    const s = ["@"].join("");
    return [e, t, n].join(s)
}
n={
     timestamp: Math.floor(Date.now() / 1e3)
}
s={
            privateKey: rS(),
}
o = {
        data: iS('/h/getgoodslistv3.php', n.timestamp, s.privateKey),
        timestamp: n.timestamp
    }
c=Bo(o.data)
  b= o.timestamp
  console.log(c,b)