/*
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-08-18 14:01:49
 * @LastEditTime: 2025-08-18 14:07:07
 * @FilePath: /逆向百例/yg.g.yuyan888.cn/run.js
 * url https://yg.g.yuyan888.cn/#/GameDetile/2000070/s/%E5%BF%83%E5%8A%A8%E5%B0%8F%E9%95%87
 */
const CryptoJS=require('crypto-js')
Bo=(t)=>{
    return CryptoJS.MD5(t).toString();
}
e = {
  gameId: "2000070",
  platformId: "2",
  platformName: "安卓",
  clientId: "",
  categoryId: 3925009,
  parentId: 3925007,
  queryType: 1,
  page: 1,
  pageSize: 10,
  serverId: "",
  serverName: "",
  sortId: 107,
  sortPanelName: "最新发布",
  searchCondition: "",
  keyword: "",
  clientName: "全部客户端",
};
Zw = () => {
    const e = "abcd5ef73gh8i194jk0lmn0opq5r2s1tu1v1wxyz";
    let t = "";
    for (let n = 0; n < 20; n++) {
        const s = Math.floor(Math.random() * e.length);
        t += e[s]
    }
    return [t.slice(0, 10), t.slice(10)]
}
t='1755496350945'
Sd = (e, t) => {
    const n = Bo(e + t + 123)
      , s = Zw();
    return (s[0] + Bo(n + t) + s[1]).split("").reverse().join("")
}
sign=Sd(JSON.stringify(e), t);
console.log(sign)