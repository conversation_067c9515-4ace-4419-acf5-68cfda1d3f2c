/**
 * url https://bqcm0.cavip1.com/
 * 登录 接口 加密
 * 加密方式 DES
 * 密钥 16位
 * 加密模式 ECB
 * 填充方式 PKCS7
 */
const CryptoJS = require("crypto-js");
let key=CryptoJS.enc.Utf8.parse("GCwCCXMBbDeKHat3");
data=CryptoJS.enc.Utf8.parse('{"username":"aaa111111111","password":"123456","captcha":"56"}')

encryptd=CryptoJS.DES.encrypt(data,key,{
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
}).toString()
console.log(encryptd)
