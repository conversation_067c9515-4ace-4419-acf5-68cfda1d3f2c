/**
 * url
 * https://xindafengche.souche.com/#/login?action=accountLogin
 */
window=global
let jsencrypt=require('jsencrypt')
let pbk="MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAhshr133pNLC52kgE1AobipfWXCsbSnsscp1sD42/9g5Gp7hNqIdLiKhSf+/Ioj30Azd5I6UlB2auZt520NamDmnIfugk0vFiZh/O0GeEsGkWUSpiTZZ5aVKM1XbPL74ZFI7weymkBIMZJsmvuBXbKaEbkjiP33qDA+P5dCgNDXGhVYGWvalkzGv9j6ErDVuDBUmHLofIU5sUGYadIl5OcIbRyeNi93DZ2V5Dx2+1+hLo6MFyaMwmtL7XfP3IwnL1OJKXZoeyvOCPjka1MsMhf8cL2w7p3VvIxIii9MoOgvJ1+ICW7+bKRDn3TfRSUzb3dPVnBwmj3KTtfhN8ZITWRwIDAQAB"
let pwd='654321'

function encrypt(pwd){
    let encryptor = new jsencrypt();
    encryptor.setPublicKey(pbk);
    return encryptor.encrypt(pwd)
}
console.log(encrypt(pwd))