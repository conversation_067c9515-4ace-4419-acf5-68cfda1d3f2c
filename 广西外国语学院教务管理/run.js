/*
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-08-17 12:41:52
 * @LastEditTime: 2025-08-17 13:21:22
 * @FilePath: /逆向百例/广西外国语学院教务管理/run.
 * url https://jwxt.gxufl.com/xtgl/login_slogin.html?time=1755405290735
 */

const axios = require('axios');

// 加载 RSA 相关函数
eval(require('fs').readFileSync(require.resolve('./loader'), 'utf8'));

// 从接口获取公钥信息并加密密码
const now = Date.now();
const url = `https://jwxt.gxufl.com/xtgl/login_getPublicKey.html?time=${now}&_=${now - 1}`;

axios.get(url)
    .then(response => {
        const { modulus, exponent } = response.data;
        const pass = '654321';

        var rsaKey = new RSAKey();
        rsaKey.setPublic(b64tohex(modulus), b64tohex(exponent));
        var enPassword = hex2b64(rsaKey.encrypt(pass));
        console.log(enPassword);
    })
    .catch(error => {
        console.error('获取公钥失败:', error.message);
    });