const CryptoJS = require("crypto-js");
var t =
  "/api/home/<USER>";
function od(e, t) {
  return CryptoJS.HmacSHA512(e, t).toString();
}
function a2() {
    a3={
    "n": 20,
    "codes": {
        "0": "W",
        "1": "l",
        "2": "k",
        "3": "B",
        "4": "Q",
        "5": "g",
        "6": "f",
        "7": "i",
        "8": "i",
        "9": "r",
        "10": "v",
        "11": "6",
        "12": "A",
        "13": "K",
        "14": "N",
        "15": "k",
        "16": "4",
        "17": "L",
        "18": "1",
        "19": "8"
    }
}
  for (
    var e = (
        arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "/"
      ).toLowerCase(),
      t = e + e,
      n = "iLAgiQ6rliBl4vl8kkiLkQBkgNk4NLvigvWlWl1iLk4NLvigvWlWlrkKAkri4iikir1iLk4NLvi4grllrkKAkri4iikir1ALBlkgkllriiLAgiQ6rliBl4vl8kkiLkQBkgNk4NLvigvWlWl1iLk4NLvigvWlWlrkKAkri4iikir1iLk4NLvi4grllrkKAkri4iikir1ALBlkgkllri",
      i = 0;
    i < t.length;
    ++i
  ) {
    var o = t[i].charCodeAt() % a3.n;
    n += a3.codes[o];
  }
  console.log(n)
  return n;
}
function a1() {
  return (0, od)(t + '{}', (0, a2)(t))
    .toLowerCase()
    .substr(8, 20);
}
 i = a1("/api/home/<USER>/api/home/<USER>")
console.log(i); 