/*
 * @LastEditors: <PERSON>shi <EMAIL>
 * @Date: 2025-08-14 17:30:57
 * @LastEditTime: 2025-08-14 17:40:13
 * @FilePath: /逆向百例/天津市公共资源交易平台/code.js
 * url http://ggzy.zwfwb.tj.gov.cn/jyxx/index_4.jhtml
 */
const CryptoJS = require('crypto-js');
var hh="http://ggzy.zwfwb.tj.gov.cn:80/jyxxcgjg/1609857.jhtml"
var s='qnbyzzwmdgghmcnm'
  var aa = hh.split("/");
    var aaa = aa.length;
    var bbb = aa[aaa - 1].split('.');
    var ccc = bbb[0];
    var cccc = bbb[1];
    var r = /^\+?[1-9][0-9]*$/;
    var ee = '_blank';
    if (r.test(ccc) && cccc.indexOf('jhtml') != -1) {
        var srcs = CryptoJS.enc.Utf8.parse(ccc);
        var k = CryptoJS.enc.Utf8.parse(s);
        var en = CryptoJS.AES.encrypt(srcs, k, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7
        });
        var ddd = en.toString();
        ddd = ddd.replace(/\//g, "^");
        ddd = ddd.substring(0, ddd.length - 2);
        var bbbb = ddd + '.' + bbb[1];
    console.log(encodeURI(bbbb))
    }
