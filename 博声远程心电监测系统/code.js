/*
 * @LastEditors: Mishi <EMAIL>
 * @Date: 2025-08-14 19:32:36
 * @LastEditTime: 2025-08-14 19:50:53
 * @FilePath: /逆向百例/博声远程心电监测系统/code.js
 * url http://cn3app.wecardio.com:28092/
 * rsa加密
 */
eval(require('fs').readFileSync(require.resolve('./loader'), 'utf8'));
    function base64ToHex(base64String){
        const buffer=Buffer.from(base64String,'base64')
        return buffer.toString('hex')
    }
           var rsaKey = new RSAKey();
            rsaKey.setPublic(base64ToHex('AJhBfuP1NpCe79fJ56SUQUFTqPcAyn5DHBIr456YXPsQsxDMVQQZiJbPOeIa9mN1/SQ1bOjQ2Lmg5UbKFliFICCqbRbaqIF/dATXgXrI+mWsNLXmRMyBACMt0zo68RIKk4/W1IhO2sS1dcPBjzhr3EHRyjnsr4VI79uAu5mntIzl'), base64ToHex('AQAB'));
            var enPassword = Buffer.from(rsaKey.encrypt('654321'), 'hex').toString('base64');
            console.log(enPassword)