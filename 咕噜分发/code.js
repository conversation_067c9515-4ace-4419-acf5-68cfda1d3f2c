/*
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-08-15 22:14:18
 * @LastEditTime: 2025-08-15 23:07:42
 * @FilePath: /逆向百例/咕噜分发/code.js
 * url https://www.gulufenfa.com/#/login
 */
const CryptoJS = require('crypto-js');
var zwe=function(t){
    return CryptoJS.MD5(t)
}
function Hwe(e) {
    const t = JSON.parse(JSON.stringify(e));
    for (const r in t) {
        const e = typeof t[r];
        (["coLicensePic", "cardPic1", "cardPic2", "cardPic3", "reject_reason", "sign"].includes(r) || "string" !== e && "number" !== e && "boolean" !== e || "string" === e && (t[r].length > 30 || t[r].includes(" ") || !t[r])) && delete t[r]
    }
    const n = Object.keys(t).sort(( (e, t) => e < t ? -1 : 1));
    let o = "";
    return n.forEach((e => {
        o += `${e}=>${t[e]}@`
    }
    )),
    o = o.substring(0, o.length - 1),
    o = zwe(o).toString(),
    o += "^_*#06@!@6#_^",
    o = zwe(o).toString().toUpperCase(),
    o
}
let password='654321'
u1={
    "password": zwe(password).toString(),
    "username":"test",
        "captToken": "acc1bee5-ac1e-627b-10a2-b6adb92de38e",

}
e={
    "password": zwe(password).toString(),
    "username": "test",
    "captType": 1,
    "pointJson": "z2+NOMOstlA13hwkIsvViKoKJDRY3LCUuudxo65hUt51Q55XKJmuod1rN8xTOPm6NfkdKwYkdeLFENYc/3maqZgVzO1CG0PluyYVGkv267c=",
    "captToken": "acc1bee5-ac1e-627b-10a2-b6adb92de38e",
    "visitorId": "25b58e0d12f6d60d2c635baf5ad133ff",
    "sign": Hwe(u1),
    "ver": "2.0",
    "mt": 1755270242233
}

console.log(Hwe(e))