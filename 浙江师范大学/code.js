/*
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-08-14 23:49:08
 * @LastEditTime: 2025-08-14 23:52:30
 * @FilePath: /逆向百例/浙江师范大学/code.js
 * url https://authserver.zjnu.edu.cn/authserver/login
 */
const CryptoJS=require('crypto-js')
function getAesString(n, f, c) {
    f = f.replace(/(^\s+)|(\s+$)/g, "");
    f = CryptoJS.enc.Utf8.parse(f);
    c = CryptoJS.enc.Utf8.parse(c);
    return CryptoJS.AES.encrypt(n, f, {
        iv: c,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
    }).toString()
}
var $aes_chars = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678"
  , aes_chars_len = $aes_chars.length;
function randomString(n) {
    var f = "";
    for (i = 0; i < n; i++)
        f += $aes_chars.charAt(Math.floor(Math.random() * aes_chars_len));
    return f
}
function encryptAES(n, f) {
    return f ? getAesString(randomString(64) + n, f, randomString(16)) : n
}
function encryptPassword(n, f) {
        return encryptAES(n, f)
}
pass="654321"
pwd=encryptPassword(pass,"a6lWHeM6BnAapqiw")
console.log(pwd)