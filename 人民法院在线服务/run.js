/*
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-08-20 18:00:04
 * @LastEditTime: 2025-08-20 18:04:05
 * @FilePath: /逆向百例/人民法院在线服务/run.js
 * url https://zxfw.court.gov.cn/zxfw/#/pagesGrxx/pc/login/index
 */
const sm2 = require('sm-crypto').sm2;

// 你的公钥和密码
const publicKey = "04008fb3cc6f176ec1a7079c11fd01777b30413d027b45f76d21567658d2f2174190e25e3841f01d39142cf8d7a9289795d41ddb5baa2ce063c9bc109a8d56521d"; // 需要替换为实际的公钥
const password = "654321";

// SM2加密
const encryptData = sm2.doEncrypt(password, publicKey, 1);
console.log(encryptData);