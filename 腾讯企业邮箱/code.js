/*
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-08-14 17:03:22
 * @LastEditTime: 2025-08-14 17:08:31
 * @FilePath: /逆向百例/腾讯企业邮箱/code.js
 */
var dbits, canary = 0xdeadbeefcafe, j_lm = 15715070 == (16777215 & canary);
function BigInteger(t, r, n) {
    null != t && ("number" == typeof t ? this.fromNumber(t, r, n) : null == r && "string" != typeof t ? this.fromString(t, 256) : this.fromString(t, r))
}
function nbi() {
    return new BigInteger(null)
}
function am1(t, r, n, i, o, e) {
    for (; 0 <= --e; ) {
        var s = r * this[t++] + n[i] + o;
        o = Math.floor(s / 67108864),
        n[i++] = 67108863 & s
    }
    return o
}
function am2(t, r, n, i, o, e) {
    for (var s = 32767 & r, h = r >> 15; 0 <= --e; ) {
        var p = 32767 & this[t]
          , a = this[t++] >> 15
          , g = h * p + a * s;
        o = ((p = s * p + ((32767 & g) << 15) + n[i] + (1073741823 & o)) >>> 30) + (g >>> 15) + h * a + (o >>> 30),
        n[i++] = 1073741823 & p
    }
    return o
}
function am3(t, r, n, i, o, e) {
    for (var s = 16383 & r, h = r >> 14; 0 <= --e; ) {
        var p = 16383 & this[t]
          , a = this[t++] >> 14
          , g = h * p + a * s;
        o = ((p = s * p + ((16383 & g) << 14) + n[i] + o) >> 28) + (g >> 14) + h * a,
        n[i++] = 268435455 & p
    }
    return o
}
dbits = j_lm && "Microsoft Internet Explorer" == navigator.appName ? (BigInteger.prototype.am = am2,
30) : j_lm && "Netscape" != navigator.appName ? (BigInteger.prototype.am = am1,
26) : (BigInteger.prototype.am = am3,
28),
BigInteger.prototype.DB = dbits,
BigInteger.prototype.DM = (1 << dbits) - 1,
BigInteger.prototype.DV = 1 << dbits;
var BI_FP = 52;
BigInteger.prototype.FV = Math.pow(2, BI_FP),
BigInteger.prototype.F1 = BI_FP - dbits,
BigInteger.prototype.F2 = 2 * dbits - BI_FP;
for (var BI_RM = "0123456789abcdefghijklmnopqrstuvwxyz", BI_RC = new Array, rr = "0".charCodeAt(0), vv = 0; vv <= 9; ++vv)
    BI_RC[rr++] = vv;
for (rr = "a".charCodeAt(0),
vv = 10; vv < 36; ++vv)
    BI_RC[rr++] = vv;
for (rr = "A".charCodeAt(0),
vv = 10; vv < 36; ++vv)
    BI_RC[rr++] = vv;
function int2char(t) {
    return BI_RM.charAt(t)
}
function intAt(t, r) {
    r = BI_RC[t.charCodeAt(r)];
    return null == r ? -1 : r
}
function bnpCopyTo(t) {
    for (var r = this.t - 1; 0 <= r; --r)
        t[r] = this[r];
    t.t = this.t,
    t.s = this.s
}
function bnpFromInt(t) {
    this.t = 1,
    this.s = t < 0 ? -1 : 0,
    0 < t ? this[0] = t : t < -1 ? this[0] = t + DV : this.t = 0
}
function nbv(t) {
    var r = nbi();
    return r.fromInt(t),
    r
}
function bnpFromString(t, r) {
    var n;
    if (16 == r)
        n = 4;
    else if (8 == r)
        n = 3;
    else if (256 == r)
        n = 8;
    else if (2 == r)
        n = 1;
    else if (32 == r)
        n = 5;
    else {
        if (4 != r)
            return void this.fromRadix(t, r);
        n = 2
    }
    this.t = 0,
    this.s = 0;
    for (var i = t.length, o = !1, e = 0; 0 <= --i; ) {
        var s = 8 == n ? 255 & t[i] : intAt(t, i);
        s < 0 ? "-" == t.charAt(i) && (o = !0) : (o = !1,
        0 == e ? this[this.t++] = s : e + n > this.DB ? (this[this.t - 1] |= (s & (1 << this.DB - e) - 1) << e,
        this[this.t++] = s >> this.DB - e) : this[this.t - 1] |= s << e,
        (e += n) >= this.DB && (e -= this.DB))
    }
    8 == n && 0 != (128 & t[0]) && (this.s = -1,
    0 < e && (this[this.t - 1] |= (1 << this.DB - e) - 1 << e)),
    this.clamp(),
    o && BigInteger.ZERO.subTo(this, this)
}
function bnpClamp() {
    for (var t = this.s & this.DM; 0 < this.t && this[this.t - 1] == t; )
        --this.t
}
function bnToString(t) {
    if (this.s < 0)
        return "-" + this.negate().toString(t);
    var r;
    if (16 == t)
        r = 4;
    else if (8 == t)
        r = 3;
    else if (2 == t)
        r = 1;
    else if (32 == t)
        r = 5;
    else {
        if (4 != t)
            return this.toRadix(t);
        r = 2
    }
    var n, i = (1 << r) - 1, o = !1, e = "", s = this.t, h = this.DB - s * this.DB % r;
    if (0 < s--)
        for (h < this.DB && 0 < (n = this[s] >> h) && (o = !0,
        e = int2char(n)); 0 <= s; )
            h < r ? (n = (this[s] & (1 << h) - 1) << r - h,
            n |= this[--s] >> (h += this.DB - r)) : (n = this[s] >> (h -= r) & i,
            h <= 0 && (h += this.DB,
            --s)),
            0 < n && (o = !0),
            o && (e += int2char(n));
    return o ? e : "0"
}
function bnNegate() {
    var t = nbi();
    return BigInteger.ZERO.subTo(this, t),
    t
}
function bnAbs() {
    return this.s < 0 ? this.negate() : this
}
function bnCompareTo(t) {
    var r = this.s - t.s;
    if (0 != r)
        return r;
    var n = this.t;
    if (0 != (r = n - t.t))
        return r;
    for (; 0 <= --n; )
        if (0 != (r = this[n] - t[n]))
            return r;
    return 0
}
function nbits(t) {
    var r, n = 1;
    return 0 != (r = t >>> 16) && (t = r,
    n += 16),
    0 != (r = t >> 8) && (t = r,
    n += 8),
    0 != (r = t >> 4) && (t = r,
    n += 4),
    0 != (r = t >> 2) && (t = r,
    n += 2),
    0 != (r = t >> 1) && (t = r,
    n += 1),
    n
}
function bnBitLength() {
    return this.t <= 0 ? 0 : this.DB * (this.t - 1) + nbits(this[this.t - 1] ^ this.s & this.DM)
}
function bnpDLShiftTo(t, r) {
    for (var n = this.t - 1; 0 <= n; --n)
        r[n + t] = this[n];
    for (n = t - 1; 0 <= n; --n)
        r[n] = 0;
    r.t = this.t + t,
    r.s = this.s
}
function bnpDRShiftTo(t, r) {
    for (var n = t; n < this.t; ++n)
        r[n - t] = this[n];
    r.t = Math.max(this.t - t, 0),
    r.s = this.s
}
function bnpLShiftTo(t, r) {
    for (var n = t % this.DB, i = this.DB - n, o = (1 << i) - 1, e = Math.floor(t / this.DB), s = this.s << n & this.DM, h = this.t - 1; 0 <= h; --h)
        r[h + e + 1] = this[h] >> i | s,
        s = (this[h] & o) << n;
    for (h = e - 1; 0 <= h; --h)
        r[h] = 0;
    r[e] = s,
    r.t = this.t + e + 1,
    r.s = this.s,
    r.clamp()
}
function bnpRShiftTo(t, r) {
    r.s = this.s;
    var n = Math.floor(t / this.DB);
    if (n >= this.t)
        r.t = 0;
    else {
        var i = t % this.DB
          , o = this.DB - i
          , e = (1 << i) - 1;
        r[0] = this[n] >> i;
        for (var s = n + 1; s < this.t; ++s)
            r[s - n - 1] |= (this[s] & e) << o,
            r[s - n] = this[s] >> i;
        0 < i && (r[this.t - n - 1] |= (this.s & e) << o),
        r.t = this.t - n,
        r.clamp()
    }
}
function bnpSubTo(t, r) {
    for (var n = 0, i = 0, o = Math.min(t.t, this.t); n < o; )
        i += this[n] - t[n],
        r[n++] = i & this.DM,
        i >>= this.DB;
    if (t.t < this.t) {
        for (i -= t.s; n < this.t; )
            i += this[n],
            r[n++] = i & this.DM,
            i >>= this.DB;
        i += this.s
    } else {
        for (i += this.s; n < t.t; )
            i -= t[n],
            r[n++] = i & this.DM,
            i >>= this.DB;
        i -= t.s
    }
    r.s = i < 0 ? -1 : 0,
    i < -1 ? r[n++] = this.DV + i : 0 < i && (r[n++] = i),
    r.t = n,
    r.clamp()
}
function bnpMultiplyTo(t, r) {
    var n = this.abs()
      , i = t.abs()
      , o = n.t;
    for (r.t = o + i.t; 0 <= --o; )
        r[o] = 0;
    for (o = 0; o < i.t; ++o)
        r[o + n.t] = n.am(0, i[o], r, o, 0, n.t);
    r.s = 0,
    r.clamp(),
    this.s != t.s && BigInteger.ZERO.subTo(r, r)
}
function bnpSquareTo(t) {
    for (var r = this.abs(), n = t.t = 2 * r.t; 0 <= --n; )
        t[n] = 0;
    for (n = 0; n < r.t - 1; ++n) {
        var i = r.am(n, r[n], t, 2 * n, 0, 1);
        (t[n + r.t] += r.am(n + 1, 2 * r[n], t, 2 * n + 1, i, r.t - n - 1)) >= r.DV && (t[n + r.t] -= r.DV,
        t[n + r.t + 1] = 1)
    }
    0 < t.t && (t[t.t - 1] += r.am(n, r[n], t, 2 * n, 0, 1)),
    t.s = 0,
    t.clamp()
}
function bnpDivRemTo(t, r, n) {
    var i = t.abs();
    if (!(i.t <= 0)) {
        var o = this.abs();
        if (o.t < i.t)
            return null != r && r.fromInt(0),
            void (null != n && this.copyTo(n));
        null == n && (n = nbi());
        var e = nbi()
          , s = this.s
          , h = t.s
          , t = this.DB - nbits(i[i.t - 1]);
        0 < t ? (i.lShiftTo(t, e),
        o.lShiftTo(t, n)) : (i.copyTo(e),
        o.copyTo(n));
        var p = e.t
          , a = e[p - 1];
        if (0 != a) {
            var o = a * (1 << this.F1) + (1 < p ? e[p - 2] >> this.F2 : 0)
              , g = this.FV / o
              , u = (1 << this.F1) / o
              , f = 1 << this.F2
              , c = n.t
              , l = c - p
              , m = null == r ? nbi() : r;
            for (e.dlShiftTo(l, m),
            0 <= n.compareTo(m) && (n[n.t++] = 1,
            n.subTo(m, n)),
            BigInteger.ONE.dlShiftTo(p, m),
            m.subTo(e, e); e.t < p; )
                e[e.t++] = 0;
            for (; 0 <= --l; ) {
                var b = n[--c] == a ? this.DM : Math.floor(n[c] * g + (n[c - 1] + f) * u);
                if ((n[c] += e.am(0, b, n, l, 0, p)) < b)
                    for (e.dlShiftTo(l, m),
                    n.subTo(m, n); n[c] < --b; )
                        n.subTo(m, n)
            }
            null != r && (n.drShiftTo(p, r),
            s != h && BigInteger.ZERO.subTo(r, r)),
            n.t = p,
            n.clamp(),
            0 < t && n.rShiftTo(t, n),
            s < 0 && BigInteger.ZERO.subTo(n, n)
        }
    }
}
function bnMod(t) {
    var r = nbi();
    return this.abs().divRemTo(t, null, r),
    this.s < 0 && 0 < r.compareTo(BigInteger.ZERO) && t.subTo(r, r),
    r
}
function Classic(t) {
    this.m = t
}
function cConvert(t) {
    return t.s < 0 || 0 <= t.compareTo(this.m) ? t.mod(this.m) : t
}
function cRevert(t) {
    return t
}
function cReduce(t) {
    t.divRemTo(this.m, null, t)
}
function cMulTo(t, r, n) {
    t.multiplyTo(r, n),
    this.reduce(n)
}
function cSqrTo(t, r) {
    t.squareTo(r),
    this.reduce(r)
}
function bnpInvDigit() {
    if (this.t < 1)
        return 0;
    var t = this[0];
    if (0 == (1 & t))
        return 0;
    var r = 3 & t;
    return 0 < (r = (r = (r = (r = r * (2 - (15 & t) * r) & 15) * (2 - (255 & t) * r) & 255) * (2 - ((65535 & t) * r & 65535)) & 65535) * (2 - t * r % this.DV) % this.DV) ? this.DV - r : -r
}
function Montgomery(t) {
    this.m = t,
    this.mp = t.invDigit(),
    this.mpl = 32767 & this.mp,
    this.mph = this.mp >> 15,
    this.um = (1 << t.DB - 15) - 1,
    this.mt2 = 2 * t.t
}
function montConvert(t) {
    var r = nbi();
    return t.abs().dlShiftTo(this.m.t, r),
    r.divRemTo(this.m, null, r),
    t.s < 0 && 0 < r.compareTo(BigInteger.ZERO) && this.m.subTo(r, r),
    r
}
function montRevert(t) {
    var r = nbi();
    return t.copyTo(r),
    this.reduce(r),
    r
}
function montReduce(t) {
    for (; t.t <= this.mt2; )
        t[t.t++] = 0;
    for (var r = 0; r < this.m.t; ++r) {
        var n = 32767 & t[r]
          , i = n * this.mpl + ((n * this.mph + (t[r] >> 15) * this.mpl & this.um) << 15) & t.DM;
        for (t[n = r + this.m.t] += this.m.am(0, i, t, r, 0, this.m.t); t[n] >= t.DV; )
            t[n] -= t.DV,
            t[++n]++
    }
    t.clamp(),
    t.drShiftTo(this.m.t, t),
    0 <= t.compareTo(this.m) && t.subTo(this.m, t)
}
function montSqrTo(t, r) {
    t.squareTo(r),
    this.reduce(r)
}
function montMulTo(t, r, n) {
    t.multiplyTo(r, n),
    this.reduce(n)
}
function bnpIsEven() {
    return 0 == (0 < this.t ? 1 & this[0] : this.s)
}
function bnpExp(t, r) {
    if (********** < t || t < 1)
        return BigInteger.ONE;
    var n, i = nbi(), o = nbi(), e = r.convert(this), s = nbits(t) - 1;
    for (e.copyTo(i); 0 <= --s; )
        r.sqrTo(i, o),
        0 < (t & 1 << s) ? r.mulTo(o, e, i) : (n = i,
        i = o,
        o = n);
    return r.revert(i)
}
function bnModPowInt(t, r) {
    r = new (t < 256 || r.isEven() ? Classic : Montgomery)(r);
    return this.exp(t, r)
}
function Arcfour() {
    this.i = 0,
    this.j = 0,
    this.S = new Array
}
function ARC4init(t) {
    for (var r, n, i = 0; i < 256; ++i)
        this.S[i] = i;
    for (i = r = 0; i < 256; ++i)
        r = r + this.S[i] + t[i % t.length] & 255,
        n = this.S[i],
        this.S[i] = this.S[r],
        this.S[r] = n;
    this.i = 0,
    this.j = 0
}
function ARC4next() {
    var t;
    return this.i = this.i + 1 & 255,
    this.j = this.j + this.S[this.i] & 255,
    t = this.S[this.i],
    this.S[this.i] = this.S[this.j],
    this.S[this.j] = t,
    this.S[t + this.S[this.i] & 255]
}
function prng_newstate() {
    return new Arcfour
}
Classic.prototype.convert = cConvert,
Classic.prototype.revert = cRevert,
Classic.prototype.reduce = cReduce,
Classic.prototype.mulTo = cMulTo,
Classic.prototype.sqrTo = cSqrTo,
Montgomery.prototype.convert = montConvert,
Montgomery.prototype.revert = montRevert,
Montgomery.prototype.reduce = montReduce,
Montgomery.prototype.mulTo = montMulTo,
Montgomery.prototype.sqrTo = montSqrTo,
BigInteger.prototype.copyTo = bnpCopyTo,
BigInteger.prototype.fromInt = bnpFromInt,
BigInteger.prototype.fromString = bnpFromString,
BigInteger.prototype.clamp = bnpClamp,
BigInteger.prototype.dlShiftTo = bnpDLShiftTo,
BigInteger.prototype.drShiftTo = bnpDRShiftTo,
BigInteger.prototype.lShiftTo = bnpLShiftTo,
BigInteger.prototype.rShiftTo = bnpRShiftTo,
BigInteger.prototype.subTo = bnpSubTo,
BigInteger.prototype.multiplyTo = bnpMultiplyTo,
BigInteger.prototype.squareTo = bnpSquareTo,
BigInteger.prototype.divRemTo = bnpDivRemTo,
BigInteger.prototype.invDigit = bnpInvDigit,
BigInteger.prototype.isEven = bnpIsEven,
BigInteger.prototype.exp = bnpExp,
BigInteger.prototype.toString = bnToString,
BigInteger.prototype.negate = bnNegate,
BigInteger.prototype.abs = bnAbs,
BigInteger.prototype.compareTo = bnCompareTo,
BigInteger.prototype.bitLength = bnBitLength,
BigInteger.prototype.mod = bnMod,
BigInteger.prototype.modPowInt = bnModPowInt,
BigInteger.ZERO = nbv(0),
BigInteger.ONE = nbv(1),
Arcfour.prototype.init = ARC4init,
Arcfour.prototype.next = ARC4next;
var rng_state, rng_psize = 256;
function rng_seed_int(t) {
    rng_pool[rng_pptr++] ^= 255 & t,
    rng_pool[rng_pptr++] ^= t >> 8 & 255,
    rng_pool[rng_pptr++] ^= t >> 16 & 255,
    rng_pool[rng_pptr++] ^= t >> 24 & 255,
    rng_psize <= rng_pptr && (rng_pptr -= rng_psize)
}
function rng_seed_time() {
    rng_seed_int((new Date).getTime())
}
if (null == rng_pool) {
    var rng_pool = new Array
      , rng_pptr = 0;
    if ("Netscape" == navigator.appName && navigator.appVersion < "5" && window.crypto)
        for (var z = window.crypto.random(32), t = 0; t < z.length; ++t)
            rng_pool[rng_pptr++] = 255 & z.charCodeAt(t);
    for (; rng_pptr < rng_psize; )
        t = Math.floor(65536 * Math.random()),
        rng_pool[rng_pptr++] = t >>> 8,
        rng_pool[rng_pptr++] = 255 & t;
    rng_pptr = 0,
    rng_seed_time()
}
function rng_get_byte() {
    if (null == rng_state) {
        for (rng_seed_time(),
        (rng_state = prng_newstate()).init(rng_pool),
        rng_pptr = 0; rng_pptr < rng_pool.length; ++rng_pptr)
            rng_pool[rng_pptr] = 0;
        rng_pptr = 0
    }
    return rng_state.next()
}
function rng_get_bytes(t) {
    for (var r = 0; r < t.length; ++r)
        t[r] = rng_get_byte()
}
function SecureRandom() {}
function parseBigInt(t, r) {
    return new BigInteger(t,r)
}
function linebrk(t, r) {
    for (var n = "", i = 0; i + r < t.length; )
        n += t.substring(i, i + r) + "\n",
        i += r;
    return n + t.substring(i, t.length)
}
function byte2Hex(t) {
    return t < 16 ? "0" + t.toString(16) : t.toString(16)
}
function pkcs1pad2(t, r) {
    if (r < t.length + 11)
        return alert("Message too long for RSA"),
        null;
    for (var n = new Array, i = t.length - 1; 0 <= i && 0 < r; )
        n[--r] = t.charCodeAt(i--);
    n[--r] = 0;
    for (var o = new SecureRandom, e = new Array; 2 < r; ) {
        for (e[0] = 0; 0 == e[0]; )
            o.nextBytes(e);
        n[--r] = e[0]
    }
    return n[--r] = 2,
    n[--r] = 0,
    new BigInteger(n)
}
function RSAKey() {
    this.n = null,
    this.e = 0,
    this.d = null,
    this.p = null,
    this.q = null,
    this.dmp1 = null,
    this.dmq1 = null,
    this.coeff = null
}
function RSASetPublic(t, r) {
    null != t && null != r && 0 < t.length && 0 < r.length ? (this.n = parseBigInt(t, 16),
    this.e = parseInt(r, 16)) : alert("Invalid RSA public key")
}
function RSADoPublic(t) {
    return t.modPowInt(this.e, this.n)
}
function RSAEncrypt(t) {
    t = pkcs1pad2(t, this.n.bitLength() + 7 >> 3);
    if (null == t)
        return null;
    t = this.doPublic(t);
    if (null == t)
        return null;
    t = t.toString(16);
    return 0 == (1 & t.length) ? t : "0" + t
}
SecureRandom.prototype.nextBytes = rng_get_bytes,
RSAKey.prototype.doPublic = RSADoPublic,
RSAKey.prototype.setPublic = RSASetPublic,
RSAKey.prototype.encrypt = RSAEncrypt;
var b64map = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"
  , b64pad = "=";
function hex2b64(t) {
    for (var r, n = "", i = 0; i + 3 <= t.length; i += 3)
        r = parseInt(t.substring(i, i + 3), 16),
        n += b64map.charAt(r >> 6) + b64map.charAt(63 & r);
    for (i + 1 == t.length ? (r = parseInt(t.substring(i, i + 1), 16),
    n += b64map.charAt(r << 2)) : i + 2 == t.length && (r = parseInt(t.substring(i, i + 2), 16),
    n += b64map.charAt(r >> 2) + b64map.charAt((3 & r) << 4)); 0 < (3 & n.length); )
        n += b64pad;
    return n
}
function b64tohex(t) {
    for (var r, n = "", i = 0, o = 0; o < t.length && t.charAt(o) != b64pad; ++o)
        v = b64map.indexOf(t.charAt(o)),
        v < 0 || (i = 0 == i ? (n += int2char(v >> 2),
        r = 3 & v,
        1) : 1 == i ? (n += int2char(r << 2 | v >> 4),
        r = 15 & v,
        2) : 2 == i ? (n += int2char(r),
        n += int2char(v >> 2),
        r = 3 & v,
        3) : (n += int2char(r << 2 | v >> 4),
        n += int2char(15 & v),
        0));
    return 1 == i && (n += int2char(r << 2)),
    n
}
function b64toBA(t) {
    for (var r = b64tohex(t), n = new Array, i = 0; 2 * i < r.length; ++i)
        n[i] = parseInt(r.substring(2 * i, 2 * i + 2), 16);
    return n
}
function safeauth_js() {}

var PublicKey = "CF87D7B4C864F4842F1D337491A48FFF54B73A17300E8E42FA365420393AC0346AE55D8AFAD975DFA175FAF0106CBA81AF1DDE4ACEC284DAC6ED9A0D8FEB1CC070733C58213EFFED46529C54CEA06D774E3CC7E073346AEBD6C66FC973F299EB74738E400B22B1E7CDC54E71AED059D228DFEB5B29C530FF341502AE56DDCFE9";
var RSA = new RSAKey();
RSA.setPublic(PublicKey, "10001");

var Res = hex2b64(RSA.encrypt("654321" + '\n' +"1755162120"+ '\n'));
console.log(Res)