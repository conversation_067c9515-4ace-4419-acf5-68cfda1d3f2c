/*
 * @LastEditors: <PERSON>shi <EMAIL>
 * @Date: 2025-08-22 20:36:01
 * @LastEditTime: 2025-08-22 20:59:48
 * @FilePath: /逆向百例/福建省公共资源交易电子公共服务平台/curl.js
 * url https://ggzyfw.fujian.gov.cn/business/list/
 */
const axios = require('axios');
const { getSign, decrypt } = require('./run.js');
async function req(){
// 构建请求数据
const requestData = {
    'pageNo': 5,
    'pageSize': 20,
    'total': 3004,
    'AREACODE': '',
    'M_PROJECT_TYPE': '',
    'KIND': 'GCJS',
    'GGTYPE': '1',
    'PROTYPE': '',
    'timeType': '6',
    'BeginTime': '2025-02-22 00:00:00',
    'EndTime': '2025-08-22 23:59:59',
    'createTime': '',
    'ts': Date.now() // 使用当前时间戳
};

// 动态生成签名
const dynamicSign = getSign(requestData);

const response = await axios.post(
  'https://ggzyfw.fujian.gov.cn/FwPortalApi/Trade/TradeInfo',
  requestData,
  {
    headers: {
      'Accept': 'application/json, text/plain, */*',
      'Accept-Language': 'zh-CN,zh;q=0.9',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Content-Type': 'application/json;charset=UTF-8',
      'Origin': 'https://ggzyfw.fujian.gov.cn',
      'Pragma': 'no-cache',
      'Referer': 'https://ggzyfw.fujian.gov.cn/business/list/',
      'Sec-Fetch-Dest': 'empty',
      'Sec-Fetch-Mode': 'cors',
      'Sec-Fetch-Site': 'same-origin',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/141.0.0.0 Safari/537.36',
      'dnt': '1',
      'portal-sign': dynamicSign,
      'sec-ch-ua': '"Google Chrome";v="141", "Not?A_Brand";v="8", "Chromium";v="141"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"Windows"',
      'sec-gpc': '1'
    }
  }
);

// 处理响应数据
const { Data } = response.data;
console.log('原始加密数据:', Data);

// 解密响应数据
const decryptedData = decrypt(Data);
console.log('解密后的数据:', decryptedData);

// 解析为JSON
const parsedData = JSON.parse(decryptedData);
console.log('解析后的JSON数据:', parsedData);
}
req()