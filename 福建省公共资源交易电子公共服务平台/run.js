/*
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-08-22 19:48:25
 * @LastEditTime: 2025-08-22 20:57:20
 * @FilePath: /逆向百例/福建省公共资源交易电子公共服务平台/run.js
 */
const CryptoJS = require("crypto-js");
let e={
    "ts": 1755863363700,
    "pageNo": 2,
    "pageSize": 20,
    "total": 2999,
    "AREACODE": "",
    "M_PROJECT_TYPE": "",
    "KIND": "GCJS",
    "GGTYPE": "1",
    "PROTYPE": "",
    "timeType": "6",
    "BeginTime": "2025-02-22 00:00:00",
    "EndTime": "2025-08-22 23:59:59",
    "createTime": ""
}
    function l(t, e) {
            return t.toString().toUpperCase() > e.toString().toUpperCase() ? 1 : t.toString().toUpperCase() == e.toString().toUpperCase() ? 0 : -1
        }
        function u(t) {
            for (var e = Object.keys(t).sort(l), n = "", a = 0; a < e.length; a++)
                if (void 0 !== t[e[a]])
                    if (t[e[a]] && t[e[a]]instanceof Object || t[e[a]]instanceof Array) {
                        var i = JSON.stringify(t[e[a]]);
                        n += e[a] + i
                    } else
                        n += e[a] + t[e[a]];
            return n
        }
        function s(e){
            return CryptoJS.MD5(e).toString()
        }

  function d(t) {
            for (var e in t)
                "" !== t[e] && void 0 !== t[e] || delete t[e];
            var n = "B3978D054A72A7002063637CCDF6B2E5"+ u(t);
            return s(n).toLocaleLowerCase()
        }
function getSign(e){
    return d(e)
}
console.log(getSign(e));
const config = {
    e: 'EB444973714E4A40876CE66BE45D5930', // AES 密钥 (hex)
    i: 'B5A8904209931867'                  // IV (hex)
};

      function decrypt(t) {
            const key = CryptoJS.enc.Utf8.parse(config.e); 
    const iv = CryptoJS.enc.Utf8.parse(config.i); 

    const decrypted = CryptoJS.AES.decrypt(t, key, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
    });

    return decrypted.toString(CryptoJS.enc.Utf8);
        }

// 导出函数供其他模块使用
module.exports = {
    getSign,
    decrypt
};
