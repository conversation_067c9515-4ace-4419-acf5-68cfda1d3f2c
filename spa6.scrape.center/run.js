/*
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-08-17 14:59:18
 * @LastEditTime: 2025-08-17 15:10:20
 * @FilePath: /逆向百例/spa6.scrape.center/run.js
 * url https://spa6.scrape.center/page/3
 */
const CryptoJS = require('crypto-js');
const axios = require('axios');
// 定义一个名为 generateToken 的函数，它接受两个参数：
// path: 一个字符串，代表需要访问的API路径 (例如 '/api/movie')
// timestampSeconds: 一个数字，代表以秒为单位的时间戳。
//                   如果调用函数时没有提供这个值，它会使用一个默认值：
//                   Math.round(Date.now() / 1000)
//                   - Date.now() 获取当前时间的毫秒数 (例如 1678886400000)
//                   - 除以 1000 转换为秒 (例如 1678886400.000)
//                   - Math.round() 对其进行四舍五入得到整数秒 (例如 1678886400)
function generateToken(path, timestampSeconds = Math.round(Date.now() / 1000)) {
    // 将传入的数字类型时间戳 (timestampSeconds) 转换成字符串类型，
    // 因为后续操作需要字符串格式。
    // 例如：1678886400 -> "1678886400"
    const timestampStr = timestampSeconds.toString();

    // 使用模板字符串将 API 路径 (path) 和 字符串时间戳 (timestampStr)
    // 用逗号 ',' 连接起来，形成一个用于生成哈希的原始数据字符串。
    // 例如：path="/api/movie", timestampStr="1678886400"
    //      -> dataToHash = "/api/movie,1678886400"
    const dataToHash = `${path},${timestampStr}`;

    // 使用 CryptoJS 库计算 SHA1 哈希值：
    // 1. CryptoJS.SHA1(dataToHash): 对 dataToHash 字符串计算 SHA1 哈希。
    //    这会返回 a CryptoJS 内部的 WordArray object.
    // 2. .toString(CryptoJS.enc.Hex): 将这个 WordArray 对象转换成
    //    十六进制表示的字符串。
    // 例如：对 "/api/movie,1678886400" 计算 SHA1
    //      -> sha1Hash = "15adce94367c6604574fd9edc8c7dc89c8a2794b" (示例，基于 timestamp 1678886400)
    const sha1Hash = CryptoJS.SHA1(dataToHash).toString(CryptoJS.enc.Hex);

    // 再次使用模板字符串，将上一步计算出的 SHA1 十六进制字符串 (sha1Hash)
    // 和原始的字符串时间戳 (timestampStr) 用逗号 ',' 连接起来，
    // 形成最终用于 Base64 编码的中间字符串。
    // 例如：sha1Hash="15adce94367c6604574fd9edc8c7dc89c8a2794b", timestampStr="1678886400"
    //      -> intermediateString = "15adce94367c6604574fd9edc8c7dc89c8a2794b,1678886400"
    const intermediateString = `${sha1Hash},${timestampStr}`;

    // 使用 Node.js 内置的 Buffer 对象进行 Base64 编码：
    // 1. Buffer.from(intermediateString, 'utf-8'):
    //    - 创建一个 Buffer 对象。
    //    - 它将 intermediateString 字符串按照 UTF-8 编码规则转换成二进制数据，
    //      并存储在这个 Buffer 对象中。
    // 2. .toString('base64'):
    //    - 将 Buffer 对象中存储的二进制数据，按照 Base64 编码规则
    //      转换成最终的 Base64 字符串。
    // 例如：对 "15adce94367c6604574fd9edc8c7dc89c8a2794b,1678886400" 进行 Base64 编码
    //      -> token = "MWE1ZGNlOTQzNjdjNjYwNDU3NGZkOWVkYzhjN2RjODljOGEyNzk0YiwxNjc4ODg2NDAw" (示例)
    //      (实际的 token 会类似 OGVkODg1N2IyZGE1NGZiMTcwNzgzZDZjZjc2OWFhODIxYjVlODE3MCwxNzU1NDE0NjIz
    //       只是 SHA1 值和时间戳不同)
    const token = Buffer.from(intermediateString, 'utf-8').toString('base64');

    // 将计算得到的 Base64 编码的 token 字符串作为函数的返回值。
    // 返回的 token 格式通常是: "Base64EncodedSHA1Hash,Base64EncodedTimestamp"
    // 但实际显示为一串 Base64 字符，因为整个 "hash,timestamp" 字符串被一起编码了。
    // 例如： "OGVkODg1N2IyZGE1NGZiMTcwNzgzZDZjZjc2OWFhODIxYjVlODE3MCwxNzU1NDE0NjIz"
    return token;
}
/**
 * 获取电影数据
 */
async function fetchMovieData() {
    const path = '/api/movie';
    const limit = 10;
    const offset = 10; // 对应 page 2
    const url = `https://spa6.scrape.center${path}`;

    // 1. 生成 token
    const token = generateToken(path);
    console.log(`Generated Token: ${token}`);

    // 2. 发送请求并获取响应
    const response = await axios.get(url, {
        params: {
            limit: limit,
            offset: offset,
            token: token
        },
        headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/141.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Referer': 'https://spa6.scrape.center/page/2',
        }
    });

    // 3. 打印数据
    console.log('--- Movie Data ---');
    console.log(JSON.stringify(response.data, null, 2));
}

// --- 执行函数 ---
fetchMovieData();
