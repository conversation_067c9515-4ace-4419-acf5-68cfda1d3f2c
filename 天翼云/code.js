/**
 * 天翼云
 * url https://www.ctyun.cn/h5/auth/login
 * password 加密
 * 
 */
const CryptoJS = require('crypto-js');

// 输入参数
const originalKey = '188121381100000000000000'; // 24 字节密钥
const desKey = CryptoJS.enc.Utf8.parse(originalKey.substring(0, 8)); // DES密钥：前8字节
const plaintext = '654321'; // 明文

// DES-ECB 加密函数
function encryptDES(key, plaintext) {
    const encrypted = CryptoJS.DES.encrypt(plaintext, key, {
        mode: CryptoJS.mode.ECB, // ECB 模式
        padding: CryptoJS.pad.Pkcs7 // PKCS7 填充
    });
    return encrypted.toString(); // 返回 Base64 编码的密文
}

// 调用加密函数
const ciphertext = encryptDES(desKey, plaintext);
console.log('DES密钥:', originalKey.substring(0, 8));
console.log('明文:', plaintext);
console.log('加密结果:', ciphertext);
console.log('目标结果:', 'yYhniS6Xpbg=');
console.log('结果匹配:', ciphertext === 'yYhniS6Xpbg=');
